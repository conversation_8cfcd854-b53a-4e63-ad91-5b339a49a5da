import json
import logging
import os
import sys
import time
from datetime import datetime, timedelta

import requests
from google.cloud import storage
from google.oauth2 import service_account
from selenium import webdriver
from selenium.common.exceptions import TimeoutException
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service as ChromeService
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import Select, WebDriverWait
from webdriver_manager.chrome import ChromeDriverManager

import cci_func as funk
import gemini_find as gf
import Legal_Summary

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout),  # Console handler with stdout
        logging.FileHandler(
            "nclat_scraper.log", encoding="utf-8"
        ),  # File handler with UTF-8 encoding
    ],
)
logger = logging.getLogger(__name__)

Headers = {
    "competition_appeal": [
        "Sr_No",
        "File_Number",
        "Case_No",
        "Case_Title",
        "Bench",
        "Status",
    ]
}

# --- Configuration ---
URL = "https://nclat.nic.in/display-board/orders"
LOCATIONS_TO_SCRAPE = ["New Delhi"]
CASE_TYPES_TO_SCRAPE = ["Competition Appeal(AT)"]

yesterday = datetime.now() - timedelta(days=4)
FROM_DATE = yesterday.strftime("%m/%d/%Y")
TO_DATE = datetime.now()
TO_DATE = TO_DATE.strftime("%m/%d/%Y")
# TO_DATE = datetime.now().strftime("%m/%d/%Y")

# Google Cloud Storage Configuration
BUCKET_NAME = "cciscrape_bkt"  # Replace with your actual bucket name
GCS_FOLDER = "NCLAT"
SERVICE_ACCOUNT_KEY_FILE = "cciscrape-d55fa0df8a7a.json"


# Initialize Google Cloud Storage client
def initialize_gcs_client():
    """Initialize Google Cloud Storage client with service account"""
    try:
        credentials = service_account.Credentials.from_service_account_file(
            SERVICE_ACCOUNT_KEY_FILE
        )
        storage_client = storage.Client(credentials=credentials)
        return storage_client
    except Exception as e:
        logger.error(f"Failed to initialize GCS client: {e}")
        return None


def setup_driver():
    """Setup Chrome driver"""
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")

    driver = webdriver.Chrome(
        service=ChromeService(ChromeDriverManager().install()), options=chrome_options
    )
    return driver


def extract_all_cases_from_tables(driver):
    """Extract data from all tables in the results"""
    all_cases = []

    tables = driver.find_elements(
        By.CSS_SELECTOR, "section.filter-sec table.table.table-hover.table-bordered"
    )
    logger.info(f"Found {len(tables)} table(s)")

    for table_index, table in enumerate(tables):
        tbody = table.find_element(By.TAG_NAME, "tbody")
        rows = tbody.find_elements(By.TAG_NAME, "tr")

        for row in rows:
            cells = row.find_elements(By.TAG_NAME, "th") + row.find_elements(
                By.TAG_NAME, "td"
            )
            if len(cells) >= 8:
                case_data = {
                    "Sr_No": cells[0].text.strip(),
                    "File_Number": cells[1].text.strip(),
                    "Case_No": cells[2].text.strip(),
                    "Case_Type": cells[3].text.strip(),
                    "Case_Title": cells[4].text.strip(),
                    "Bench": cells[5].text.strip(),
                    "Status": cells[6].text.strip(),
                }
                all_cases.append(case_data)

    return all_cases


def extract_first_order_history(driver, filing_no, case_no):
    """Extract first order history and download PDF if available"""
    try:
        wait = WebDriverWait(driver, 30)
        order_table = wait.until(
            EC.visibility_of_element_located(
                (By.CSS_SELECTOR, "table.table-bordered.table-hover.table-striped")
            )
        )

        rows = order_table.find_elements(By.TAG_NAME, "tr")
        if len(rows) > 1:
            first_row = rows[1]
            cells = first_row.find_elements(By.TAG_NAME, "td")

            txt = []
            for each in cells:
                txt.append(each.text.strip())
            # print('cells',cells[0].text.strip())

            if len(txt) >= 4:
                order_data = {
                    "Sr_No": txt[0],
                    "Order_Date": txt[1],
                    "Order_Type": txt[2],
                    "Coram": txt[3],
                }

                # Check for download button and download PDF
                if len(cells) > 4:
                    download_forms = cells[4].find_elements(By.TAG_NAME, "form")
                    if download_forms:
                        form = download_forms[0]
                        form_data = {}
                        for inp in form.find_elements(By.TAG_NAME, "input"):
                            name = inp.get_attribute("name")
                            value = inp.get_attribute("value")
                            if name and value:
                                form_data[name] = value

                        # Download PDF
                        print("here")
                        try:
                            pdf_file, name = download_pdf(
                                driver,
                                form_data,
                                filing_no,
                                case_no,
                                order_data["Order_Date"],
                            )
                            print("pdf_file", pdf_file)

                        except Exception as e:
                            print("Exception", e)

                        # Exception case
                        order_date = order_data["Order_Date"]
                        safe_case_no = "".join(
                            c for c in case_no if c.isalnum() or c in (" ", "-", "_")
                        ).rstrip()
                        safe_filing_no = "".join(
                            c for c in filing_no if c.isalnum() or c in (" ", "-", "_")
                        ).rstrip()
                        name = f"{safe_filing_no}_{safe_case_no}_{order_date}.pdf"

                        npath = f"gs://{BUCKET_NAME}/NCLAT/{name}"
                        print(npath)

                        order_data["Path"] = npath

                        gemini_summary = Legal_Summary.generate_legal_summary(npath)
                        gemini_summary = "<<<END_OF_ITEM>>>".join(gemini_summary)

                        order_data["order_summary"] = gemini_summary

                        return order_data

                return order_data

        return {"Error": "No order history found"}

    except Exception as e:
        return {"Error": f"Could not extract order history: {str(e)}"}


def download_pdf(driver, form_data, filing_no, case_no, order_date):
    """Download PDF and upload to Google Cloud Storage"""
    try:
        safe_case_no = "".join(
            c for c in case_no if c.isalnum() or c in (" ", "-", "_")
        ).rstrip()
        safe_filing_no = "".join(
            c for c in filing_no if c.isalnum() or c in (" ", "-", "_")
        ).rstrip()
        filename = f"{safe_filing_no}_{safe_case_no}_{order_date}.pdf"

        session = requests.Session()
        for cookie in driver.get_cookies():
            session.cookies.set(cookie["name"], cookie["value"])

        response = session.post(
            "https://nclat.nic.in/display-board/view_order",
            data=form_data,
            stream=True,
            timeout=30,
        )

        if response.status_code == 200:
            content_type = response.headers.get("content-type", "").lower()
            if "pdf" in content_type or response.content.startswith(b"%PDF"):

                # Initialize GCS client
                storage_client = initialize_gcs_client()
                if not storage_client:
                    logger.error("Failed to initialize GCS client")
                    return None

                # Get bucket and blob
                bucket = storage_client.bucket(BUCKET_NAME)
                blob_name = f"{GCS_FOLDER}/{filename}"
                blob = bucket.blob(blob_name)

                # Check if blob already exists
                if blob.exists():
                    logger.info(
                        f"⚠️ Duplicate found, skipping upload: gs://{BUCKET_NAME}/{blob_name}"
                    )
                    return filename  # Still return filename for record

                # Upload PDF content directly to GCS
                pdf_content = response.content
                blob.upload_from_string(pdf_content, content_type="application/pdf")

                logger.info(f"✅ Uploaded PDF to GCS: gs://{BUCKET_NAME}/{blob_name}")
                return filename, blob_name

        return None

    except Exception as e:
        logger.error(f"Error downloading/uploading PDF: {e}")
        return None


def find_toggle_button(driver, filing_no):
    """Find toggle button for specific filing number"""
    toggle_buttons = driver.find_elements(
        By.CSS_SELECTOR, "section.filter-sec .Toggletab[data-ids]"
    )
    for button in toggle_buttons:
        if button.get_attribute("data-ids") == filing_no:
            return button
    return None


def scrape_nclat_orders():
    """Main scraping function"""
    # Initialize GCS client first
    storage_client = initialize_gcs_client()
    if not storage_client:
        logger.error("Failed to initialize Google Cloud Storage client. Exiting.")
        return

    # Verify bucket exists
    try:
        bucket = storage_client.bucket(BUCKET_NAME)
        bucket.reload()  # This will raise an exception if bucket doesn't exist
        logger.info(f"[SUCCESS] Connected to GCS bucket: {BUCKET_NAME}")
    except Exception as e:
        logger.error(f"[ERROR] Failed to access bucket {BUCKET_NAME}: {e}")
        return

    driver = setup_driver()
    wait = WebDriverWait(driver, 30)
    all_cases_data = []

    try:
        logger.info(
            f"Searching for Location: {LOCATIONS_TO_SCRAPE[0]}, Case Type: {CASE_TYPES_TO_SCRAPE[0]}"
        )
        logger.info(f"[GCS] PDFs will be stored in: gs://{BUCKET_NAME}/{GCS_FOLDER}/")

        # Load page and navigate to Daily Order date tab
        driver.get(URL)
        wait.until(EC.presence_of_element_located((By.CLASS_NAME, "caselist")))
        time.sleep(2)

        # Find and scroll to the Daily Order date tab before clicking
        daily_order_tab = driver.find_element(
            By.XPATH, "//li[contains(text(), 'Daily Order date')]"
        )
        driver.execute_script(
            "arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});",
            daily_order_tab,
        )
        time.sleep(1)

        # Try multiple click methods
        try:
            # Method 1: Regular click
            daily_order_tab.click()
            logger.info("[SUCCESS] Daily Order date tab clicked using regular click")
        except Exception as e:
            logger.info(f"Regular click failed: {e}")
            try:
                # Method 2: JavaScript click
                driver.execute_script("arguments[0].click();", daily_order_tab)
                logger.info("[SUCCESS] Daily Order date tab clicked using JavaScript")
            except Exception as e:
                logger.info(f"JavaScript click failed: {e}")
                # Method 3: Actions click
                actions = ActionChains(driver)
                actions.move_to_element(daily_order_tab).click().perform()
                logger.info("[SUCCESS] Daily Order date tab clicked using Actions")

        time.sleep(2)

        # Fill and submit form
        location_select = Select(driver.find_element(By.ID, "location"))
        location_select.select_by_value("delhi")

        case_type_select = Select(driver.find_element(By.ID, "case_type"))
        case_type_select.select_by_value("34")

        driver.execute_script(
            f"document.getElementById('from_date').value = '{FROM_DATE}';"
        )
        driver.execute_script(
            f"document.getElementById('to_date').value = '{TO_DATE}';"
        )

        form = driver.find_element(By.ID, "FormId")
        form.submit()

        # Wait for results and extract all cases
        wait.until(EC.presence_of_element_located((By.ID, "table-container")))
        time.sleep(15)

        all_cases = extract_all_cases_from_tables(driver)

        if not all_cases:
            logger.info("No results found.")
            return

        logger.info(f"Found {len(all_cases)} case(s) across all tables. Processing...")

        # Process each case
        for i, case_data in enumerate(all_cases):
            filing_no = case_data["File_Number"]
            logger.info(
                f"Processing case {i+1}/{len(all_cases)} - Filing No: {filing_no}"
            )

            # Find and click toggle button
            toggle_button = find_toggle_button(driver, filing_no)
            if toggle_button:
                driver.execute_script(
                    "arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});",
                    toggle_button,
                )
                time.sleep(1)
                toggle_button.click()

                # Wait for expanded section
                expanded_section_id = f"demo_{filing_no}"
                wait.until(
                    EC.visibility_of_element_located((By.ID, expanded_section_id))
                )
                time.sleep(5)

                # Extract first order history
                print(filing_no)
                print(case_data["Case_No"])

                first_order = extract_first_order_history(
                    driver, filing_no, case_data["Case_No"]
                )

                print(first_order)

                # Combine data
                complete_case = {**case_data, "First_Order_History": first_order}

                all_cases_data.append(complete_case)
                logger.info(f"[SUCCESS] Processed: {filing_no}")

                if first_order.get("Downloaded_File"):
                    logger.info(f"[PDF] Downloaded: {first_order['Downloaded_File']}")
            else:
                logger.info(f"[ERROR] Toggle button not found for: {filing_no}")

            time.sleep(2)

    except Exception as e:
        logger.error(f"Error: {e}")
    finally:
        # Save files to db
        if all_cases_data:

            final_data = {
                "total_cases": len(all_cases_data),
                "date_range": f"{FROM_DATE} to {TO_DATE}",
                "scraping_timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "cases": all_cases_data,
            }

            total_pdfs = sum(
                1
                for case in all_cases_data
                if case.get("First_Order_History", {}).get("path")
            )

            data = final_data["cases"]

            print(data[0])

            toRet = {}
            for i in range(0, len(data)):
                data[i]["index"] = (
                    data[i]["Case_No"]
                    + "-"
                    + data[i]["First_Order_History"]["Order_Date"]
                )
                data[i]["path"] = data[i]["First_Order_History"]["Path"]
                data[i]["order_summary"] = data[i]["First_Order_History"][
                    "order_summary"
                ]
                data[i]["First_Order_History"].pop(
                    "order_summary", None
                )  # Remove if not needed
                data[i]["First_Order_History"].pop("Path", None)

                toRet[i] = data[i]

            # data[i]['First_Order_History']=json.dumps(data[i]['First_Order_History'])

            logger.info(f"\n[COMPLETE] Processed {len(all_cases_data)} cases")
            logger.info(f"[PDF] Uploaded {total_pdfs} PDFs to GCS")

            path = f"gs://{BUCKET_NAME}/{GCS_FOLDER}/"
            logger.info(f"[GCS] Location: {path}")

            # #Now, to write:
            # funk.write(data,'competition_appeal_orders',)
            return toRet

        driver.quit()
