from google.cloud import bigquery


class BigQueryReader:
    def __init__(self, client, project, dataset):
        self.client = client
        self.project = project
        self.dataset = dataset

    def read_table(self, table_name):
        table_id = f"{self.project}.{self.dataset}.{table_name}"
        query = f"SELECT * FROM `{table_id}`"
        query_job = self.client.query(query)
        return [dict(row) for row in query_job]

    def read_query(self, query):
        query_job = self.client.query(query)
        return [dict(row) for row in query_job]


class BigQueryWriter:
    def __init__(self, client, project, dataset):
        self.client = client
        self.project = project
        self.dataset = dataset

    def write_rows(self, table_name, rows):
        """
        rows: list of dicts
        """
        table_id = f"{self.project}.{self.dataset}.{table_name}"
        errors = self.client.insert_rows_json(table_id, rows)
        if errors:
            raise Exception(f"BigQuery insert errors: {errors}")
        return True

    def overwrite_table(self, table_name, rows):
        """
        Overwrites the table with the given rows (list of dicts).
        """
        table_id = f"{self.project}.{self.dataset}.{table_name}"
        job_config = bigquery.LoadJobConfig(write_disposition="WRITE_TRUNCATE")
        job = self.client.load_table_from_json(rows, table_id, job_config=job_config)
        job.result()  # Wait for job to complete
        return True
