import logging
import os
import sys

from google import genai
from google.genai.types import HttpOptions, Part
from google.oauth2 import service_account

# --- Configuration ---

# Set environment variables for Vertex AI
os.environ["GOOGLE_GENAI_USE_VERTEXAI"] = "True"
# Replace with your actual Google Cloud project ID and location
os.environ["GOOGLE_CLOUD_PROJECT"] = "cciscrape"
os.environ["GOOGLE_CLOUD_LOCATION"] = "global"  # Changed to match official example
os.environ["Google_api_key"] = "AIzaSyANs-Etuztf0YlgI1zTeB0gv2HDW93Sh5A"

# Initialize credentials from service account file
credentials = service_account.Credentials.from_service_account_file(
    "cciscrape-d55fa0df8a7a.json",
    scopes=["https://www.googleapis.com/auth/cloud-platform"],
)

# --- Main Functions ---


def get_insights_from_gemini(gcs_uri):
    """
    Sends a PDF from GCS to Gemini via Vertex AI for analysis.

    Args:
        gcs_uri (str): The GCS URI of the PDF (e.g., gs://bucket_name/path/to/file.pdf).

    Returns:
        str: The insights generated by the model, or None on error.
    """
    try:
        # Initialize the client for Vertex AI with credentials
        client = genai.Client(
            credentials=credentials, http_options=HttpOptions(api_version="v1")
        )

        # Prepare the PDF part from the GCS URI
        pdf_file = Part.from_uri(file_uri=gcs_uri, mime_type="application/pdf")

        # Define the prompt
        prompt = """
        Extract from this court order:
        • What is the core legal issue being addressed?
        • What was decided and why? 
        • What are the practical implications of this order?

        Focus on substance, not case details. Be specific and concise. 
        Each point max 20 words.
        """

        logging.info(f"Sending PDF {gcs_uri} to Gemini for analysis...")

        # Specify model
        model_name = "gemini-2.5-flash"  # Updated to latest model

        # Generate content using the correct client.models.generate_content method
        response = client.models.generate_content(
            model=model_name,
            contents=[pdf_file, prompt],  # Changed order to match official example
        )

        logging.info("Successfully received insights from Gemini.")

        return response.text

    except Exception as e:
        logging.error(f"Failed to get insights from Gemini: {e}", exc_info=True)
        return None
