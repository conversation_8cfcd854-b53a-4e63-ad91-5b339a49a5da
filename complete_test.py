import logging
import time

from google.cloud import bigquery

import cci_func as funk
import Legal_Summary

Approved_emails = ["<EMAIL>", "<EMAIL>"]
Schemas = {
    "Combination": {
        "Section 31": [
            "col_no",
            "combination_registration_no",
            "notifying_parties",
            "form",
            "date_of_notification",
            "status",
            "decision_date",
            "summary_as_submitted_by_parties",
            "order_link",
            "media",
        ],
        "Section 43a and 44": [
            "col_no",
            "combination_registration_no",
            "description",
            "under_section",
            "decision_date",
            "order_link",
            "media",
        ],
        "Cases Approved with Modification (s)": [
            "col_no",
            "case_no",
            "parties_name",
            "date_of_order",
        ],
    },
    "Antitrust": {
        "Orders": [
            "col_no",
            "case_no",
            "description",
            "type",
            "date_of_main_order",
            "date_of_order",
            "orders",
        ],
        "Press Releases": ["col_no", "title", "date_of_release", "document"],
    },
    "NCLAT": {
        "Competition Appeal Orders": [
            "Sr_No",
            "File_Number",
            "Case_No",
            "Case_Title",
            "Bench",
            "Status",
        ]
    },
}

table_map = {
    "Section 31": "section_31",
    "Section 43a and 44": "section_43a_and_44",
    "Cases Approved with Modification (s)": "cases_approved_with_modifications",
    "Orders": "antitrust_orders",
    "Press Releases": "press_releases",
    "Competition Appeal Orders": "competition_appeal_orders",
}


def setup_logging(level, filename):
    # Get the root logger
    root_logger = logging.getLogger()

    # Remove all existing handlers to prevent unexpected log files
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
        handler.close()  # Close the handler to release any file locks

    # Set up basic configuration for file logging
    logging.basicConfig(
        level=level,
        format="%(asctime)s - %(levelname)s - %(message)s",
        filename=filename,  # Log file name
        filemode="w",  # Overwrite log file on each run
    )

    # Add a stream handler for command-line output (console)
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)
    console_handler.setFormatter(
        logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
    )
    root_logger.addHandler(console_handler)

    # Prevent propagation from child loggers (e.g., from imported modules)
    root_logger.propagate = False


setup_logging(logging.INFO, "test_run.log")


def send_email(subject, body, email):
    # Only send to the allowed email
    if email not in Approved_emails:
        logging.warning(f"Attempted to send email to unauthorized address: {email}")
        return
    try:
        funk.sendEmail(subject, body, email)
        logging.info(f"Email sent to {email}")
    except Exception as e:
        logging.error(f"Failed to send email to {email}: {e}")


def test_scrape_and_send():
    logging.info("Testing scrape and send...")
    for category in Schemas:
        for key in Schemas[category]:
            logging.info(f"Scraping: Category={category}, Key={key}")
            try:
                # Scrape only 1 row for minimal testing
                data = funk.mainCall(category, key)
                if not data or len(data) == 0:
                    logging.info(f"No data found for {category} - {key}")
                    continue
                row = data[0] if isinstance(data, list) else list(data.values())[0]
                logging.info(f"Scraped row: {row}")

                # Generate summary for the row
                summary = []
                if "path" in row and row["path"]:
                    summary = Legal_Summary.generate_legal_summary(row["path"], row)
                    logging.info(f"Generated summary: {summary}")
                else:
                    logging.info("No PDF path found for summary generation.")

                # add the summary to the row:
                row["test_summary"] = (
                    "<<<END_OF_ITEM>>>".join(summary) if summary else ""
                )

                # Prepare email
                table = table_map.get(key)
                subject, body = Legal_Summary.coreEmail([row], table)
                logging.info(f"Prepared email for {key}")

                # Send email <NAME_EMAIL>
                for email in Approved_emails:
                    send_email(subject, body, email)

                time.sleep(2)
            except Exception as e:
                logging.error(f"Error processing {category} - {key}: {e}")


def test_db_read_and_send():
    logging.info("Testing DB read and send...")
    bq_client = bigquery.Client.from_service_account_json("cciscrape-d55fa0df8a7a.json")
    dataset = "scrapedb"
    for category in Schemas:
        for key in Schemas[category]:
            table = table_map.get(key)
            if not table:
                logging.info(f"No table mapping for {key}")
                continue
            logging.info(f"Reading from DB: Table={table}")
            try:
                data = funk.read(key, top=1)
                if not data or len(data) == 0:
                    logging.info(f"No DB data found for {key}")
                    continue
                row = data[0] if isinstance(data, list) else list(data.values())[0]
                logging.info(f"DB row: {row}")

                # Generate summary for the row
                summary = []
                if "path" in row and row["path"]:
                    summary = Legal_Summary.generate_legal_summary(row["path"], row)
                    # row["summary_list"] = summary
                    logging.info(f"Generated summary: {summary}")
                else:
                    logging.info("No PDF path found for summary generation.")

                # replace the old summary with the new one:
                summary_cols = [col for col in row.keys() if col.endswith("_summary")]
                logging.info(f"Summary columns: {summary_cols}")

                longest_summary = ""
                longest_summary_col = ""
                for col in summary_cols:
                    val = row.get(col, "")
                    if isinstance(val, str) and len(val) > len(longest_summary):
                        longest_summary = val
                        longest_summary_col = col

                if longest_summary_col:
                    logging.info(f"Adding summary to column: {longest_summary_col}")
                    logging.info(f"Added summary: {summary}")
                    row[longest_summary_col] = "<<<END_OF_ITEM>>>".join(summary)

                logging.info(f"Row after summary addition: {row}")

                # Prepare email
                subject, body = Legal_Summary.coreEmail([row], table)
                logging.info(f"Prepared email for {key}")

                # Send email <NAME_EMAIL>
                for email in Approved_emails:
                    send_email(subject, body, email)

                time.sleep(2)
            except Exception as e:
                logging.error(f"Error processing DB for {category} - {key}: {e}")


if __name__ == "__main__":
    logging.info("Starting complete schema test (scrape and DB)...")
    # test_scrape_and_send()
    test_db_read_and_send()
    logging.info("Test complete.")
