import copy
import json
import os
import re
import time

import bs4 as bs
import numpy as np
import pandas as pd
import requests
import urllib3
from bs4 import BeautifulSoup
from google.cloud import storage
from selenium import webdriver
from selenium.webdriver.chrome.options import Options

import gemini_find as gf
import Legal_Summary

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

import re
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders

import google.generativeai as genai
from google.cloud import bigquery

import bigquery_io as bq
import nclat_func as nclat

#### Initial URL Set and Schema for CCI Tables
Urls = {
    "Combination": {
        "Section 31": "https://www.cci.gov.in/combination/orders-section31",
        "Section 43a and 44": "https://www.cci.gov.in/combination/orders-section43a_44",
        "Cases Approved with Modification (s)": "https://www.cci.gov.in/combination/cases-approved-with-modification",
    },
    "Antitrust": {
        "Orders": "https://www.cci.gov.in/antitrust/orders",
        "Press Releases": "https://www.cci.gov.in/antitrust/press-release",
    },
    "NCLAT": {"Competition Appeal Orders": "https://nclat.nic.in/display-board/orders"},
}
Schemas = {
    "Section 31": [
        "col_no",
        "combination_registration_no",
        "notifying_parties",
        "form",
        "date_of_notification",
        "status",
        "decision_date",
        "summary_as_submitted_by_parties",
        "order_link",
        "media",
    ],
    "Section 43a and 44": [
        "col_no",
        "combination_registration_no",
        "description",
        "under_section",
        "decision_date",
        "order_link",
        "media",
    ],
    "Cases Approved with Modification (s)": [
        "col_no",
        "case_no",
        "parties_name",
        "date_of_order",
    ],
    "Orders": [
        "col_no",
        "case_no",
        "description",
        "type",
        "date_of_main_order",
        "date_of_order",
        "orders",
    ],
    "Press Releases": ["col_no", "title", "date_of_release", "document"],
}

GeminiKey = "AIzaSyANs-Etuztf0YlgI1zTeB0gv2HDW93Sh5A"


###Fetches Static HTML
def fetch_html(url):
    try:
        H_Dict = {
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:69.0) Gecko/20100101 Firefox/69.0",
            "Accept-Language": "en-US",
        }

        response = requests.get(url, headers=H_Dict, verify=False)
        response.raise_for_status()  # Raise an exception for HTTP errors

        soup = BeautifulSoup(response.text)

        return soup

    except requests.exceptions.RequestException as e:
        print(f"Error fetching {url}: {e}")
        return None


# Fetches dynamic HTML
def fetch_dynamic_html(url):
    options = Options()
    options.add_argument("--headless")
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-dev-shm-usage")

    driver = webdriver.Chrome(options=options)
    driver.get(url)

    time.sleep(8)  # Wait for JS to load the table
    html = driver.page_source
    driver.quit()

    return html


# Finds headers in CCI table.
def getHeaders(dump):
    toRet = []
    h = dump.find_all("thead")

    # Go to the full dump, incase it found many theads
    for each in h:
        # Go to each line
        new = str(each).split("\n")

        for line in new:
            if "width=" in line:
                line = line.split("width=")[1]

                # Extract
                substr = line.split("<")[0]
                substr = substr.split(">")[1]

                toRet.append(substr)

        return toRet


def deepPull(url):
    try:
        inpage = fetch_html(url)
        dump = inpage.find_all(string=re.compile("viewPdf", re.IGNORECASE))

        for each in dump:
            if ".pdf" in each:
                sub = each.split("https://")[1]
                sub = sub.split(".pdf")[0]
                new = "https://" + sub + ".pdf"
                return new

    except Exception as e:
        print(f"Error in deepPull for {url}: {e}")
        return None


# Finds Rows in CCI table
def getRows(dump, schema, table):
    folder_dict = {
        "Section 31": "section_31",
        "Section 43a and 44": "section_43a_and_44",
        "Cases Approved with Modification (s)": "cases_approved_with_modifications",
        "Press Releases": "press_releases",
        "Orders": "antitrust_orders",
        "Competition Appeal Orders": "NCLAT",
    }

    print(f"Fetching rows for table: {table}")
    folder = folder_dict[table]
    print("Folder:", folder)

    toRet = {}

    id = ""

    classes = ["even", "odd"]

    for c in classes:
        rows = dump.find_all("tr", class_=c)

        for each in rows:
            data = {}
            cells = each.find_all("td")

            for i in range(0, len(cells)):
                data[schema[i]] = cells[i].text.strip()

                # Handle Link
                lw = [
                    "order",
                    "document",
                    "orders",
                    "date_of_order",
                    "order_link",
                    "summary_as_submitted_by_parties",
                    "media",
                ]
                if schema[i] in lw:
                    print(schema[i])
                    data[schema[i]] = (
                        cells[i].find("a")["href"] if cells[i].find("a") else None
                    )

                    print(data[schema[i]])

                    pdflink = deepPull(
                        data[schema[i]]
                    )  # Fetch the PDF link if it exists

                    print(pdflink)
                    if pdflink:
                        # Checks if file with same name:
                        if table == "Cases Approved with Modification (s)":
                            index = data["case_no"]

                        elif table == "Section 31":
                            index = (
                                data["combination_registration_no"]
                                + "-"
                                + data["decision_date"]
                            )

                        elif table == "Section 43a and 44":
                            index = (
                                data["combination_registration_no"]
                                + "-"
                                + data["decision_date"]
                            )

                        elif table == "Press Releases":
                            index = data["date_of_release"]

                        # Designed to be unique filename in storage
                        filename = (
                            (index + "-" + str(schema[i]))
                            .replace("/", "-")
                            .replace(" ", "_")
                            .replace(":", "-")
                        )
                        print("Filename", filename)

                        # Save path to row
                        data["path"] = f"gs://cciscrape_bkt/{folder}/{filename}.pdf"

                        # Download to the path
                        blob, status = download_pdf_to_gcs(pdflink, filename, folder)

                        # Upload from Path to Gemini
                        gemini_summary = "Repeat"
                        if status == True:
                            gemini_summary = Legal_Summary.generate_legal_summary(
                                pdf_path=f"gs://cciscrape_bkt/{folder}/{filename}.pdf",
                                case_info=copy.deepcopy(
                                    data
                                ),  # TODO: @Adi check if this dic has all the metadata needed for the case
                            )
                            gemini_summary = "<<<END_OF_ITEM>>>".join(gemini_summary)

                        data[schema[i] + "_summary"] = (
                            gemini_summary  # Add summary to data
                        )

            # If case:
            if "case_no" in schema:
                toRet[data["case_no"]] = data

            elif "combination_registration_no" in schema:
                toRet[data["combination_registration_no"]] = data

            elif "title" in schema:
                toRet[data["title"]] = data

            else:
                raise ("Error: Unsupported Schema", schema)

    return toRet


# Separate handling for antitrust orders via an api call
def antitrustCall(counter=10):
    # Making API request on this one
    url = "https://www.cci.gov.in/antitrust/orders/list"

    headers = {
        "User-Agent": "Mozilla/5.0",
        "Content-Type": "application/x-www-form-urlencoded",
    }
    response = requests.get(url, headers=headers, verify=False, timeout=10)

    Jsonfile = json.loads(response.text)["data"]

    StoreDict = {}
    for each in Jsonfile:
        this = {}
        this["id"] = each["id"]
        this["case_no"] = each["case_no"]
        this["description"] = each["description"]
        this["type"] = each["type"]
        this["main_order_date"] = each["main_order_date"]
        this["order_date"] = each["order_date"]
        this["url"] = f"https://www.cci.gov.in/antitrust/orders/details/{this['id']}/0"

        pdflink = deepPull(this["url"])  # Fetch the PDF link if it exists

        # Test for AI Summary
        filename = (
            (this["case_no"] + "-" + this["order_date"])
            .replace("/", "-")
            .replace(" ", "_")
            .replace(":", "-")
        )  # Equal to index
        path = f"gs://cciscrape_bkt/antitrust_orders/{filename}.pdf"

        # print("Filename",filename)

        this["path"] = path
        blob, status = download_pdf_to_gcs(pdflink, filename, "antitrust_orders")

        gemini_summary = "Repeat"

        if status == True:
            gemini_summary = Legal_Summary.generate_legal_summary(
                pdf_path=f"gs://cciscrape_bkt/antitrust_orders/{filename}.pdf",
                case_info=copy.deepcopy(
                    this
                ),  # TODO: @Adi check if this dic has all the metadata needed for the case
            )
            gemini_summary = "<<<END_OF_ITEM>>>".join(gemini_summary)
        # print(gemini_summary)

        this["order_summary"] = gemini_summary  # Add summary to data
        StoreDict[this["id"]] = this

        counter -= 1
        if counter <= 0:
            break

    return StoreDict

def section31Call(counter=50):
  # URL where DataTables sends the POST request
  url = "https://www.cci.gov.in/combination/orders-section31"

  # This mimics the DataTables request payload
  payload = {
      "draw": 1,               # DataTables draw counter
      "start": 0,              # starting record index
      "length": 10,            # number of records to return
      "form_type": "",         # filters from form
      "order_status": "",
      "searchString": "",
      "search_type": "",
      "fromdate": "",
      "todate": "",
      "order[0][column]": 0,   # sorting by first column
      "order[0][dir]": "desc", # descending
      "columns[0][data]": "DT_RowIndex",
      "columns[0][name]": "DT_RowIndex",
      "columns[1][data]": "combination_no",
      "columns[1][name]": "combination_no",
      "columns[2][data]": "party_name",
      "columns[2][name]": "party_name",
      "columns[3][data]": "form_type",
      "columns[3][name]": "form_type",
      "columns[4][data]": "notification_date",
      "columns[4][name]": "notification_date",
      "columns[5][data]": "order_status",
      "columns[5][name]": "order_status",
      "columns[6][data]": "decision_date",
      "columns[6][name]": "decision_date",
      "columns[7][data]": "summary_files",
      "columns[7][name]": "summary_files",
      "columns[8][data]": "order_files",
      "columns[8][name]": "order_files",
  }

  # Sometimes you need headers to mimic a browser
  headers = {
      "User-Agent": "Mozilla/5.0",
      "X-Requested-With": "XMLHttpRequest",
  }

  # GET request
  try:
    data = requests.get(url, headers=headers, verify=False).json()["data"]

    counter=min(counter,len(data))

    StoreDict={}

    for i in range(0,counter):
      this={}
      this['col_no']=data[i]['id']
      this['combination_registration_no']=data[i]['combination_no']
      this['notifying_parties']=data[i]['party_name']
      this['form']=data[i]['form_type']
      this['date_of_notification']=data[i]['notification_date']
      this['status']=data[i]['order_status']
      this['decision_date']=data[i]['decision_date']
      this['media']=data[i]['media_file']

      sum_path='https://www.cci.gov.in/images/summaryorders/en/'
      order_path='https://www.cci.gov.in/images/caseorders/en/'

      this['summary_as_submitted_by_parties']=data[i]['summary_file_content']
      this['order_link']=data[i]['order_file_content']
      
      #Try to grab links
      if data[i]['summary_file_content']:
        try:
          temp=data[i]['summary_file_content'].split(".pdf")[0].split("/")[-1]+".pdf"
          this['summary_as_submitted_by_parties']=f'{sum_path}{temp}'
        
        except Exception as e:
          print("Failed to read sum file content",e)
          
      if data[i]['order_file_content']:
        try:
          temp=data[i]['order_file_content'].split(".pdf")[0].split("/")[-1]+".pdf"
          this['order_link']=f'{order_path}{temp}'
        
        except Exception as e:
          print('Failed to read ord sum',e)
      
      #Create Index
      this['path']=''
      this['index']=this['combination_registration_no']+"-"+this['decision_date']
      this['summary_as_submitted_by_parties_summary']=''
      this['order_link_summary']=''
      
      #File downloads

      for ftype in ['summary_as_submitted_by_parties','order_link']:
          filename = (
            (this["index"] + "-" + ftype)
            .replace("/", "-")
            .replace(" ", "_")
            .replace(":", "-")
        ) 
          if this[ftype]:

              print("Writing", ftype)
              path = f"gs://cciscrape_bkt/section_31/{filename}.pdf"

              this["path"] = path
              blob, status = download_pdf_to_gcs(this[ftype], filename, "section_31")

              gemini_summary = "Repeat"

              if status == True:
                  print("True status",this['path'])
                  gemini_summary = Legal_Summary.generate_legal_summary(
                      pdf_path=f"gs://cciscrape_bkt/section_31/{filename}.pdf",
                      case_info=copy.deepcopy(
                          this
                      ) )
                  gemini_summary = "<<<END_OF_ITEM>>>".join(gemini_summary)
                  this[ftype + "_summary"] = gemini_summary  # Add summary to data

      StoreDict[this['combination_registration_no']]=this

      # print('Sum',data[i]['summary_file_content'])
      # print('Order',data[i]['order_file_content'])
    return StoreDict
    
  except Exception as e:

    print("Exception",e)
    return


# Main Data Pull Function
def mainCall(Cat, key):
    url = Urls[Cat][key]

    ##Making relevant calls
    if Cat == "Antitrust" and key == "Orders":
        data = antitrustCall()

    elif Cat == "NCLAT" and key == "Competition Appeal Orders":
        data = nclat.scrape_nclat_orders()

    elif Cat == "Combination" and key == "Section 31":
        data = section31Call()
    else:
        dynamic = BeautifulSoup(fetch_dynamic_html(url), "html.parser")

        data = getRows(dynamic, Schemas[key], key)

    ##End of relevant calls
    if key == "Cases Approved with Modification (s)":
        for row in data:
            data[row]["index"] = data[row]["case_no"] + "-" + data[row]["date_of_order"]

    elif key == "Orders":
        for row in data:
            data[row]["index"] = data[row]["case_no"] + "-" + data[row]["order_date"]

    # elif key == "Section 31":
    #     for row in data:
    #         data[row]["index"] = (
    #             data[row]["combination_registration_no"]
    #             + "-"
    #             + data[row]["decision_date"]
    #         )
    elif key == "Section 43a and 44":
        for row in data:
            data[row]["index"] = (
                data[row]["combination_registration_no"]
                + "-"
                + data[row]["decision_date"]
            )

    id_tab_list = {
        "Section 31": ("index", "section_31"),
        "Section 43a and 44": ("index", "section_43a_and_44"),
        "Cases Approved with Modification (s)": (
            "index",
            "cases_approved_with_modifications",
        ),
        "Press Releases": ("title", "press_releases"),
        "Orders": ("index", "antitrust_orders"),
        "Competition Appeal Orders": ("index", "competition_appeal_orders"),
    }

    id, table = id_tab_list[key]

    try:
        dlist = list(data.values())
        write(dlist, table, id)
    # dlist=[list(each.values()) for each in dlist]
    except Exception as e:
        print(f"Error converting data to list: {e}")
        dlist = data
        write(dlist, table, id)

    return dlist


# Sends email on Gmail SMTP
def sendEmail(subject, body, receiver_email, sender_email="<EMAIL>", pdf_attachments=None):
    """
    Sends email with optional PDF attachments.

    Args:
        subject (str): Email subject
        body (str): HTML email body
        receiver_email (str): Recipient email address
        sender_email (str): Sender email address
        pdf_attachments (list): List of tuples [(pdf_bytes, filename), ...] for NCLAT cases
    """
    app_password = "aqqdgqcwsftvydrw"

    # Create the email
    message = MIMEMultipart("mixed")  # Changed to "mixed" to support attachments
    message["Subject"] = subject
    message["From"] = sender_email
    message["To"] = receiver_email

    # Add body (plain text or HTML)
    message.attach(MIMEText(body, "html"))

    # Add PDF attachments if provided (for NCLAT cases)
    if pdf_attachments:
        for pdf_bytes, filename in pdf_attachments:
            if pdf_bytes and filename:
                # Create attachment
                attachment = MIMEBase('application', 'pdf')
                attachment.set_payload(pdf_bytes)
                encoders.encode_base64(attachment)
                attachment.add_header(
                    'Content-Disposition',
                    f'attachment; filename="{filename}"'
                )
                message.attach(attachment)
                print(f"Added PDF attachment: {filename}")

    # Send via Gmail SMTP
    with smtplib.SMTP_SSL("smtp.gmail.com", 465) as server:
        server.login(sender_email, app_password)
        server.send_message(message)

    attachment_info = f" with {len(pdf_attachments)} PDF attachment(s)" if pdf_attachments else ""
    return "Email sent successfully to {}{}".format(receiver_email, attachment_info)


#############################################################
# Write to BQ
def write(data, table, index_col, dataset="scrapedb"):
    # Set up BigQuery client and writer
    bq_client = bigquery.Client.from_service_account_json("cciscrape-d55fa0df8a7a.json")
    project = bq_client.project
    dataset = dataset
    table = table

    # Use BigQueryWriter from bigquery_io.py
    writer = bq.BigQueryWriter(bq_client, project, dataset)

    # Fetch existing ids from the table (assume column is named 'col_no' as per your request)
    existing_ids = set()
    try:
        query = f"SELECT {index_col} FROM `{project}.{dataset}.{table}`"
        query_job = bq_client.query(query)
        for row in query_job:
            existing_ids.add(str(row[f"{index_col}"]))
    except Exception as e:
        print(f"Warning: Could not fetch existing ids from BigQuery: {e}")
    
    print(len(existing_ids), "existing ids found in BigQuery.")
    new_rows = [row for row in data if str(row[f"{index_col}"]) not in existing_ids]
    
    ##################################### Section 31 check
    if table=="section_31":
        # Query for data where order link is null
        non_null_ids = set()
        try:
            query = f"SELECT index FROM `cciscrape.scrapedb.section_31` WHERE order_link IS NOT NULL"
            query_job = bq_client.query(query)

            try:
              for row in query_job:
                  non_null_ids.add(row['index'])
                          
            except Exception as e:
                print("Issue fetching non null ids")
                non_null_ids=set()
            
            #Now we have two lists, we can comb through our rows.
            new_rows=[]

            print("NonNull",non_null_ids)
            print("Existing",existing_ids)

            for row in data:
                thisindex=str(row[f"{index_col}"])
                print(thisindex, "is the index" )

                if (thisindex in list(non_null_ids))==False: #If you're not in non null ids.
                    
                    if(thisindex in list(existing_ids)):   #If you are in eexisting ids, then hadle
                
                        if row['order_link_summary']!='':
                            # If order_link is not empty, we will  write the row
                            print("Made it to query2")
                            new_rows.append(row)
                            query2 = f"DELETE FROM `cciscrape.scrapedb.section_31` WHERE {index_col}='{thisindex}'"
                            query_job2 = bq_client.query(query2)
                            print(query_job2)
                    else:
                        new_rows.append(row)
                
        ##################################### Section 31 check

        except Exception as e:
            print(f"Warning: Could not fetch existing ids from non null: {e}")

    if len(new_rows) > 0:
        if "First_Order_History" in new_rows[0]:
            for i in range(0, len(new_rows)):
                new_rows[i]["First_Order_History"] = json.dumps(
                    new_rows[i]["First_Order_History"]
                )

    if len(new_rows) > 0:
        try:
            writer.write_rows(table, new_rows)
            print(f"Successfully wrote {len(new_rows)} new rows to {dataset}.{table}")

        except Exception as e:
            print(f"Error writing to BigQuery: {e}")

        # Send Email has to happen
        # Check if this is an NCLAT case (competition_appeal_orders)
        is_nclat_case = table == "competition_appeal_orders"

        if is_nclat_case:
            # For NCLAT cases, get email content and PDF paths
            email_result = Legal_Summary.coreEmail(new_rows, table)
            if len(email_result) == 3:
                subject, body, pdf_paths = email_result
            else:
                subject, body = email_result
                pdf_paths = []
        else:
            # For CCI cases, use normal email generation
            subject, body = Legal_Summary.coreEmail(new_rows, table)
            pdf_paths = []

        print("Got Here")

        try:
            mails = get_all_emails()

            if is_nclat_case and pdf_paths:
                # For NCLAT cases, download PDFs and attach them
                pdf_attachments = []
                for pdf_path in pdf_paths:
                    pdf_bytes, filename = download_pdf_from_gcs(pdf_path)
                    if pdf_bytes and filename:
                        pdf_attachments.append((pdf_bytes, filename))

                # Send emails with PDF attachments
                for email in mails:
                    sendEmail(subject, body, email, pdf_attachments=pdf_attachments)
                print(f"Emails sent to {len(mails)} subscribers with {len(pdf_attachments)} PDF attachments.")
            else:
                # For CCI cases or NCLAT without PDFs, send normal emails
                for email in mails:
                    sendEmail(subject, body, email)
                print(f"Emails sent to {len(mails)} subscribers.")

        except Exception as e:
            print(f"Error sending emails: {e}")

    else:
        print("No new rows to write. All ids already exist in the table.")


def read(section, top=10, dataset="scrapedb"):
    order_tab_list = {
        "Section 31": (f"PARSE_DATE('%d/%m/%Y', decision_date)", "section_31"),
        "Section 43a and 44": (
            f"PARSE_DATE('%d/%m/%Y', decision_date)",
            "section_43a_and_44",
        ),
        "Cases Approved with Modification (s)": (
            "case_no",
            "cases_approved_with_modifications",
        ),
        "Press Releases": (
            f"PARSE_DATE('%d/%m/%Y', date_of_release)",
            "press_releases",
        ),
        "Orders": ("CAST(id AS INT64)", "antitrust_orders"),
        "Competition Appeal Orders": (
            "PARSE_DATE('%m-%d-%Y',JSON_VALUE(First_Order_History, '$.Order_Date'))",
            "competition_appeal_orders",
        ),
    }

    table = order_tab_list[section][1]
    order = order_tab_list[section][0]

    bq_client = bigquery.Client.from_service_account_json("cciscrape-d55fa0df8a7a.json")
    project = bq_client.project
    dataset = dataset
    table = table

    try:
        # Get last 10 entries

        query = f"SELECT * FROM `{project}.{dataset}.{table}` ORDER BY ({order}) DESC LIMIT {top};"
        query_job = bq_client.query(query)
        results = [dict(row) for row in query_job]
        return results
    except Exception as e:
        print(f"Error reading from BigQuery: {e}")
        return {}


#############################################################


#########################
EMAIL_FILE = os.path.join("data", "emails.txt")

# New Subscriber Email Flow


def newSubscriber(receiver_email):
    subject = "Welcome to Vidhi.AI"
    body = f"Hello,\n\nThank you for subscribing to Vidhi AI!"
    body += f"\n\nYou will now receive updates on the latest CCI combinations/ anti-trust orders + NCLAT Competition Appeal Orders."
    body += f"\n\n\n\n Check out the sample below!"
    body += "<br><br>Sincerely,<br>Vidhi AI"
    body += f"<br><br><br>"

    data = read("Orders", top=2)
    email_result = Legal_Summary.coreEmail(data, "antitrust_orders")
    if len(email_result) == 3:
        subject2, body2, pdf_paths = email_result
    else:
        subject2, body2 = email_result

    body += body2

    body += "<br><br>"

    sendEmail(subject, body, receiver_email)

    print("New subscriber email sent to {}".format(receiver_email))


def get_all_emails():
    if not os.path.exists(EMAIL_FILE):
        return []
    with open(EMAIL_FILE, "r") as f:
        emails = [line.strip() for line in f if line.strip()]
    return emails


def save_all_emails(emails):
    with open(EMAIL_FILE, "w") as f:
        for email in emails:
            f.write(email + "\n")


def is_valid_email(email):
    # Simple regex for email validation
    return re.match(r"^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$", email)


# Core Email formatter for data. No Summary Yet.
def coreEmail(new_rows, table):
    tdict = {
        "section_31": "Section 31 Case",
        "section_43a_and_44": "Section 43a and 44 Case",
        "cases_approved_with_modifications": "Approved with Modification (s) Case",
        "antitrust_orders": "Antitrust Order",
        "press_releases": "Antitrust Press Release",
        "competition_appeal_orders": "Competition Appeal Order",
    }
    subject = f"New {tdict[table]}"

    prep = "are"
    s = "s"
    if len(new_rows) <= 1:
        prep = "is"
        s = ""

    body = f"There {prep} {len(new_rows)} new {tdict[table]}{s}. Here are the details below:<br><br>"

    if not new_rows:
        return

    # Get headers from the first row
    if "First_Order_History" in new_rows[0]:
        for i in range(0, len(new_rows)):
            new_rows[i]["First_Order_History"] = json.loads(
                new_rows[i]["First_Order_History"]
            )
            new_rows[i]["Order_Date"] = new_rows[i]["First_Order_History"]["Order_Date"]
            new_rows[i]["Order_Type"] = new_rows[i]["First_Order_History"]["Order_Type"]
            new_rows[i]["Coram"] = new_rows[i]["First_Order_History"]["Coram"]
            new_rows[i].pop(
                "First_Order_History", None
            )  # Remove the nested dict to flatten the structure

    headers = list(new_rows[0].keys())

    # Table style
    table_style = "border-collapse:collapse;width:100%;"
    th_style = "background-color:#003366;color:#fff;padding:8px;font-size:1.1em;"
    td_style = "border:1px solid #ddd;padding:6px;font-size:1em;"

    # Build HTML table
    body += f'<table style="{table_style}">'  # Table start
    # Header row
    body += "<tr>"
    for h in headers:
        body += f'<th style="{th_style}">{h.replace("_", " ").title()}</th>'
    body += "</tr>"

    # Data rows
    for row in new_rows:
        body += "<tr>"
        for h in headers:
            val = row[h]
            # If value looks like a URL, hyperlink it
            if isinstance(val, str) and (
                val.startswith("http://") or val.startswith("https://")
            ):
                val = f'<a href="{val}" style="color:#1a0dab;">{val}</a>'
            body += f'<td style="{td_style}">{val if val is not None else ""}</td>'
        body += "</tr>"
    body += "</table>"

    # Add footer
    body += "<br><br>Sincerely,<br>Vidhi AI"
    return subject, body


###########################


def gemini_pdf_summary(pdf_path, api_key=GeminiKey):
    genai.configure(api_key=api_key)
    model = genai.GenerativeModel("models/gemini-1.5-pro-latest")

    # Read PDF as bytes
    with open(pdf_path, "rb") as f:
        pdf_bytes = f.read()

    prompt = (
        "You are a legal assistant for a lawyer in India. "
        "Summarize the attached PDF in 3-5 concise bullet points, "
        "focusing on the most important legal and factual aspects relevant to Indian law."
    )

    # Gemini expects files as a list of dicts
    file_dict = {"mime_type": "application/pdf", "data": pdf_bytes}
    response = model.generate_content([prompt, file_dict])

    # Extract and return the summary (as a list of bullet points)
    summary = response.text
    bullets = [
        line.strip("-• ")
        for line in summary.splitlines()
        if line.strip().startswith(("-", "•"))
    ]
    return bullets if bullets else [summary]


def download_pdf_to_gcs(
    url,
    filename,
    folder,
    BUCKET_NAME="cciscrape_bkt",
    gcp_credentials_path="cciscrape-d55fa0df8a7a.json",
):
    """
    Downloads PDFs from the given URLs and uploads them to the specified GCP storage bucket.
    Each PDF is named after the last part of its URL (or a fallback name).
    Returns a list of GCS blob names uploaded.
    """

    storage_client = storage.Client.from_service_account_json(gcp_credentials_path)
    bucket = storage_client.bucket(BUCKET_NAME)

    blob_name = ""
    try:
        response = requests.get(url, stream=True, verify=False)
        response.raise_for_status()

        if not filename.lower().endswith(".pdf"):
            filename += ".pdf"
        blob_name = f"{folder}/{filename}"
        blob = bucket.blob(blob_name)

        if blob.exists():
            print(f"⚠️ Duplicate found, skipping upload: gs://{BUCKET_NAME}/{blob_name}")
            return filename, False  # Still return filename for record

        blob.upload_from_string(response.content, content_type="application/pdf")
        print(f"Uploaded {url} to gs://{BUCKET_NAME + '/' + folder}/{blob_name}")
    except Exception as e:
        print(f"Failed to upload {url}: {e}")

    return blob_name, True


def download_pdf_from_gcs(gcs_path, gcp_credentials_path="cciscrape-d55fa0df8a7a.json"):
    """
    Downloads a PDF from GCS bucket and returns the content as bytes for email attachment.

    Args:
        gcs_path (str): GCS path like 'gs://bucket_name/folder/filename.pdf'
        gcp_credentials_path (str): Path to GCP service account credentials

    Returns:
        tuple: (pdf_bytes, filename) or (None, None) if error
    """
    try:
        # Parse GCS path
        if not gcs_path.startswith('gs://'):
            print(f"Invalid GCS path format: {gcs_path}")
            return None, None

        # Remove 'gs://' and split bucket and blob path
        path_parts = gcs_path[5:].split('/', 1)
        if len(path_parts) != 2:
            print(f"Invalid GCS path format: {gcs_path}")
            return None, None

        bucket_name, blob_path = path_parts
        filename = blob_path.split('/')[-1]  # Extract filename from path

        # Initialize GCS client
        storage_client = storage.Client.from_service_account_json(gcp_credentials_path)
        bucket = storage_client.bucket(bucket_name)
        blob = bucket.blob(blob_path)

        # Check if blob exists
        if not blob.exists():
            print(f"PDF not found in GCS: {gcs_path}")
            return None, None

        # Download PDF content
        pdf_bytes = blob.download_as_bytes()
        print(f"Successfully downloaded PDF from GCS: {filename}")

        return pdf_bytes, filename

    except Exception as e:
        print(f"Error downloading PDF from GCS {gcs_path}: {e}")
        return None, None
