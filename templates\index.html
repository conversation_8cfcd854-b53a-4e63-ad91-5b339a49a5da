<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Vidhi.AI</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet">
  <style>
    body { font-family: 'Inter', sans-serif; }
    .hidden { display: none; }
  </style>
</head>
<body class="bg-gray-100 text-gray-900">
  <div class="flex h-screen">
    <!-- Sidebar -->
    <div class="w-64 bg-white shadow-xl p-4 overflow-y-auto">
      <div>
        <button onclick="toggleMenu('comboMenu')" class="w-full text-left py-2 px-4 text-lg font-semibold hover:bg-gray-200 rounded">Combination</button>
        <div id="comboMenu" class="ml-4 mt-2 hidden space-y-1">
          <button onclick="loadData('Section 31')" class="block w-full text-left px-4 py-2 hover:bg-blue-100 rounded">Section 31</button>
          <button onclick="loadData('Section 43a and 44')" class="block w-full text-left px-4 py-2 hover:bg-blue-100 rounded">Section 43a and 44</button>
          <button onclick="loadData('Cases Approved with Modification (s)')" class="block w-full text-left px-4 py-2 hover:bg-blue-100 rounded">Cases Approved with Modification</button>
        </div>
      </div>
      <div class="mt-4">
        <button onclick="toggleMenu('antitrustMenu')" class="w-full text-left py-2 px-4 text-lg font-semibold hover:bg-gray-200 rounded">Antitrust</button>
        <div id="antitrustMenu" class="ml-4 mt-2 hidden space-y-1">
          <button onclick="loadData('Orders')" class="block w-full text-left px-4 py-2 hover:bg-blue-100 rounded">Orders</button>
          <button onclick="loadData('Press Releases')" class="block w-full text-left px-4 py-2 hover:bg-blue-100 rounded">Press Releases</button>
        </div>
      </div>
      <div class="mt-8">
        <button onclick="toggleMenu('nclatMenu')" class="w-full text-left py-2 px-4 text-lg font-semibold hover:bg-gray-200 rounded">NCLAT</button>
        <div id="nclatMenu" class="ml-4 mt-2 hidden space-y-1">
          <button onclick="loadData('Competition Appeal Orders')" class="block w-full text-left px-4 py-2 hover:bg-blue-100 rounded">Competition Appeals</button>
        </div>
      </div>
      <!-- Email Subscription UI -->
      <div class="mt-8">
      <!-- NCLAT Section -->
      
        <button onclick="toggleMenu('subscribersMenu')" class="w-full text-left py-2 px-4 text-lg font-semibold hover:bg-gray-200 rounded">Subscribers</button>
        <div id="subscribersMenu" class="mt-2 hidden">
          <form id="email-form" class="flex space-x-2 mb-2">
            <input id="email-input" type="email" placeholder="Enter email" class="flex-1 px-2 py-1 border rounded focus:outline-none focus:ring-2 focus:ring-blue-400" required>
            <button type="submit" class="w-8 h-8 min-w-0 px-0 py-0 bg-blue-600 text-white rounded hover:bg-blue-700 text-lg flex items-center justify-center ml-1 mr-1">+</button>
          </form>
          <div class="text-xs text-red-500 mb-2" id="email-error"></div>
          <div class="h-48 overflow-y-auto border rounded bg-gray-50" id="email-list-container">
            <ul id="email-list" class="divide-y divide-gray-200"></ul>
          </div>
        </div>
      </div>
    </div>

    <!-- Content -->
    <div class="flex-1 p-6 overflow-y-auto">
      <h1 class="text-2xl font-bold mb-4">Vidhi.AI</h1>
      <!-- Section header for dynamic title -->
      <div id="section-header" class="text-xl font-semibold text-blue-800 mb-4"></div>
      <table class="w-full text-left table-fixed border-collapse shadow-sm rounded bg-white">
        <thead class="bg-gray-200 text-gray-700" id="table-head"></thead>
        <tbody id="table-body" class="divide-y divide-gray-200"></tbody>
      </table>
      <!-- Refresh Button -->
      <button id="refresh-btn" onclick="runJob()" title="Refresh Data" class="absolute top-4 right-8 z-50 bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-full shadow flex items-center gap-2">
        <svg id="refresh-icon" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="4" d="M4 4v5h.582M20 20v-5h-.581M5.635 19.364A9 9 0 1021 12.001h-1.5"/></svg>
        <span id="refresh-text">Refresh</span>
        <svg id="refresh-spinner" class="hidden animate-spin h-5 w-5 ml-2 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"></path></svg>
      </button>
    </div>
  </div>

  <script>
    function toggleMenu(menuId) {
      const menu = document.getElementById(menuId);
      menu.classList.toggle('hidden');
    }

    // Mapping from section to parent header
    const sectionToHeader = {
      'Section 31': 'Combination',
      'Section 43a and 44': 'Combination',
      'Cases Approved with Modification (s)': 'Combination',
      'Orders': 'Antitrust',
      'Press Releases': 'Antitrust',
      'Competition Appeal Orders': 'NCLAT',
      'Competition Appeals': 'NCLAT'
    };

    // For NCLAT button, map to correct section
    const buttonToSection = {
      'Competition Appeals': 'Competition Appeal Orders'
    };

    async function loadData(section) {
      // Map button label to backend section if needed
      let backendSection = buttonToSection[section] || section;

      // Set the section header (big, bold, blue)
      const sectionHeaderDiv = document.getElementById('section-header');
      if (sectionToHeader[section]) {
        sectionHeaderDiv.textContent = sectionToHeader[section] + ' > ' + section;
      } else {
        sectionHeaderDiv.textContent = '';
      }

      const res = await fetch('/get_data', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ section: backendSection })
      });
      const data = await res.json();

      const head = document.getElementById('table-head');
      const body = document.getElementById('table-body');
      head.innerHTML = '';
      body.innerHTML = '';

      if (data.length === 0) {
        body.innerHTML = '<tr><td class="p-4 text-gray-500">No data available.</td></tr>';
        return;
      }

      // Use the order of keys as received from the backend (insertion order)
      const keys = [];
      for (const k in data[0]) { if (Object.prototype.hasOwnProperty.call(data[0], k)) keys.push(k); }
      const colClass = `px-4 py-2 w-1/${keys.length} break-words align-top`;
      head.innerHTML = '<tr>' + keys.map(k => `<th class="${colClass}">${k}</th>`).join('') + '</tr>';
      data.forEach(row => {
        body.innerHTML += '<tr>' + keys.map(k => {
          let val = row[k];
          // If value is a URL, replace with hyperlink
          if (typeof val === 'string' && val.match(/^https?:\/\//)) {
            return `<td class="${colClass}"><a href="${val}" target="_blank" rel="noopener noreferrer" class="text-blue-600 underline">Link</a></td>`;
          } else {
            return `<td class="${colClass}">${val}</td>`;
          }
        }).join('') + '</tr>';
      });
    }

    // EMAIL SUBSCRIPTION LOGIC
    async function fetchEmails() {
      const res = await fetch('/emails');
      const emails = await res.json();
      renderEmailList(emails);
    }

    function renderEmailList(emails) {
      const list = document.getElementById('email-list');
      list.innerHTML = '';
      emails.forEach(email => {
        const li = document.createElement('li');
        li.className = 'flex items-center justify-between px-2 py-1 hover:bg-gray-100';
        li.innerHTML = `<span>${email}</span><button class="text-red-500 hover:text-red-700 ml-2 font-bold" onclick="deleteEmail('${email}')">&times;</button>`;
        list.appendChild(li);
      });
    }

    document.getElementById('email-form').addEventListener('submit', async function(e) {
      e.preventDefault();
      const input = document.getElementById('email-input');
      const errorDiv = document.getElementById('email-error');
      const email = input.value.trim();
      errorDiv.textContent = '';
      if (!email) return;
      const res = await fetch('/add_email', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email })
      });
      const result = await res.json();
      if (result.success) {
        input.value = '';
        fetchEmails();
      } else {
        errorDiv.textContent = result.error || 'Invalid email.';
      }
    });

    async function deleteEmail(email) {
      const res = await fetch('/delete_email', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email })
      });
      const result = await res.json();
      if (result.success) {
        fetchEmails();
      }
    }

    // On page load, fetch emails
    window.addEventListener('DOMContentLoaded', fetchEmails);

    // Ensure Subscribers section is toggled independently
    // (toggleMenu is already defined and used for other menus)

    async function runJob() {
      const btn = document.getElementById('refresh-btn');
      const icon = document.getElementById('refresh-icon');
      const spinner = document.getElementById('refresh-spinner');
      const text = document.getElementById('refresh-text');
      btn.disabled = true;
      icon.classList.add('hidden');
      spinner.classList.remove('hidden');
      text.textContent = 'Refreshing...';
      try {
        const res = await fetch('/job');
        if (res.ok) {
          alert('Data refresh complete!');
        } else {
          alert('Refresh failed.');
        }
      } catch (e) {
        alert('Error running job.');
      }
      icon.classList.remove('hidden');
      spinner.classList.add('hidden');
      text.textContent = 'Refresh';
      btn.disabled = false;
    }
  </script>
</body>
</html>
