
import json
import requests
import cci_func as funk
from google.cloud import storage

import Legal_Summary
from google.cloud import bigquery
import bigquery_io as bq

import nclat_func as nclat
import ssl
import urllib3
from urllib.parse import urlparse

import gemini_find as gf

from dotenv import load_dotenv

load_dotenv()
# Disable SSL warnings for unverified requests
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def antitrustCall():
  #Making API request on this one
  url = "https://www.cci.gov.in/antitrust/orders/list"

  headers = {
      "User-Agent": "Mozilla/5.0",
      "X-CSRF-TOKEN": "aZyX3gMwGEaWVd28h8gG0uDVlBzuxty5ppiQJR1i",
      "Content-Type": "application/x-www-form-urlencoded"
  }

  data = {
      "fromdate": "05/07/2025",  # dd/mm/yyyy format used in the site
      "todate": "03/08/2025",
      "tab_type": "orderdate"
  }
  response = requests.get("https://www.cci.gov.in/antitrust/orders/list", headers=headers, data=data, verify=False)

  print("Response",response)

  JSON=json.loads(response.text)['data']

  ToRet=[]
  
  for each in JSON:

    this={}
    this['id']=each['id']
    this['case_no']=each['case_no']
    this['description']=each['description']
    this['type']=each['type']
    this['main_order_date']=each['main_order_date']
    this['order_date']=each['order_date']
    this['url']=f"https://www.cci.gov.in/antitrust/orders/details/{this['id']}/0"

    ToRet.append(this)

  
  return ToRet[:10]


################
Schemas={'Combination':
         {'Section 31': ['col_no', 'combination_registration_no', 'notifying_parties', 'form', 'date_of_notification', 'status', 'decision_date', 'summary_as_submitted_by_parties', 'order_link', 'media'],
           'Section 43a and 44': ['col_no', 'combination_registration_no', 'description', 'under_section', 'decision_date', 'order_link', 'media'],
            'Cases Approved with Modification (s)': ['col_no', 'case_no', 'parties_name', 'date_of_order']},
              
              
              'Antitrust':
        { 'Orders': ['col_no', 'case_no', 'description', 'type', 'date_of_main_order', 'date_of_order', 'orders'],
          'Press Releases': ['col_no', 'title', 'date_of_release', 'document']},
          
            'NCLAT':
            {'Competition Appeal Orders': ['Sr_No','File_Number','Case_No','Case_Title','Bench','Status']}

          }





if __name__ == "__main__":
    print("Test Run")
    # Get data from antitrustCall
    # data1 = funk.mainCall('Antitrust','Orders')
    # urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

    # url='https://www.cci.gov.in/images/antitrustorder/en/order1754064453.pdf'

    # response = requests.get(url, verify=False)

    data=funk.mainCall('NCLAT','Competition Appeal Orders')


    # # funk.download_pdf_to_gcs(url,'antitrust_orders')

    # print(response.status_code)
    # print(response.headers.get("Content-Type"))

    # print("Init storage client")
    # storage_client = storage.Client.from_service_account_json('cciscrape-d55fa0df8a7a.json')

    # print("Here")
    # bucket = storage_client.bucket('cciscrape_bkt')
    
    # print("Fail Point")
    # filename = url.split('/')[-1]

    # blob_name = f"antitrust_orders/{filename}"

    
    # blob = bucket.blob(blob_name)

    # blob.upload_from_string(response.content, content_type='application/pdf')

    # print('Antitrust PDF Downloaded',blob)

  #   Schemas={'Combination':
  #        {'Section 31': ['col_no', 'combination_registration_no', 'notifying_parties', 'form', 'date_of_notification', 'status', 'decision_date', 'summary_as_submitted_by_parties', 'order_link', 'media'],
  #          'Section 43a and 44': ['col_no', 'combination_registration_no', 'description', 'under_section', 'decision_date', 'order_link', 'media'],
  #           'Cases Approved with Modification (s)': ['col_no', 'case_no', 'parties_name', 'date_of_order']},
              

  #             'Antitrust':
  #       { 'Orders': ['col_no', 'case_no', 'description', 'type', 'date_of_main_order', 'date_of_order', 'orders'],
  #         'Press Releases': ['col_no', 'title', 'date_of_release', 'document']},
          
  #           'NCLAT':
  #           {'Competition Appeal Orders': ['Sr_No','File_Number','Case_No','Case_Title','Bench','Status']}
  #         }

    

  #   order_tab_list={
  #     'Section 31': (f"PARSE_DATE('%d/%m/%Y', decision_date)",'section_31'),
  #     'Section 43a and 44': (f"PARSE_DATE('%d/%m/%Y', decision_date)",'section_43a_and_44'),
  #     'Cases Approved with Modification (s)': ("case_no",'cases_approved_with_modifications'),
  #     'Press Releases': (f"PARSE_DATE('%d/%m/%Y', date_of_release)",'press_releases'),
  #     'Orders':('CAST(id AS INT64)','antitrust_orders'),
  #     'Competition Appeal Orders': ('index','competition_appeal_orders')
  # }

    
  #   tabs=[]
    
  #   for key in order_tab_list:
  #       table = order_tab_list[key][1]
  #       tabs.append(table)
    
  #   print(tabs)
    

# with open("data/Axiom5.txt", "r", encoding="utf-8") as f:
#     axiom = f.read().strip()

#     # emails=[email for email in axiom.split('\n')]
#     # emails.append('<EMAIL>')

#     bq_client = bigquery.Client.from_service_account_json('cciscrape-d55fa0df8a7a.json')
#     project = bq_client.project
#     dataset = 'scrapedb'
#     table = 'section_31'

#     data=funk.read('Section 31',top=1)
    
#     subject,body= Legal_Summary.coreEmail(data,table)
#     emails=['<EMAIL>','<EMAIL>']
#     for email in emails:
#         funk.sendEmail(subject, body, email)

# bq_client = bigquery.Client.from_service_account_json("cciscrape-d55fa0df8a7a.json")
# project = bq_client.project
# dataset = 'scrapedb'

# # Use BigQueryWriter from bigquery_io.py
# writer = bq.BigQueryWriter(bq_client, project, dataset)
# non_null_ids = set()
# query = f"SELECT index FROM `cciscrape.scrapedb.section_31` WHERE order_link IS NOT NULL"
# query_job = bq_client.query(query)
# for row in query_job:
#   non_null_ids.add(row['index'])

# print(non_null_ids)
  # print(funk.deepPull("https://www.cci.gov.in/combination/order/details/summary/1592/0/orders-section31"))


    # funk.mainCall('NCLAT','Competition Appeal Orders')

    


    # print(data)




    # print("Antitrust Call Over")

    # counter=0
    # for key in data:
    #    path=data[key]['path']
    #    summary=Legal_Summary.generate_legal_summary(path)
    #    data[key]['summary_list']=summary

    #    counter+=1

    #    if counter==3:
    #       break
  

    # # dlist=[data[row] for row in data]

    # keys = list(data.keys())
    # vals=list(data.values())

    # dlist=[vals[i] for i in range(0,len(vals))]

    # subject,body = LegSum.coreEmail(dlist,'antitrust_orders')
    # funk.sendEmail(subject, body, '<EMAIL>')
    


    
       

            




    
    

    
