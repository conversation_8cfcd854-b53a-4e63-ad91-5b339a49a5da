#!/usr/bin/env python3
"""
Simple test to verify email attachment functionality works
"""

import cci_func as funk

def create_dummy_pdf():
    """Create a dummy PDF content for testing"""
    # This creates a minimal PDF structure
    dummy_pdf_content = b"""%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
100 700 Td
(Test NCLAT PDF) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000206 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
300
%%EOF"""
    return dummy_pdf_content

def test_basic_email_attachment():
    """Test basic email attachment functionality"""
    print("🧪 Testing Basic Email Attachment Functionality")
    print("="*50)
    
    # Create test email content
    subject = "🧪 Test NCLAT Email with PDF Attachment"
    body = """
    <div style="font-family: Arial, sans-serif;">
        <h2>🧪 Test NCLAT Email</h2>
        <div style="background-color: #e8f5e8; padding: 15px; border-left: 4px solid #28a745; margin: 20px 0;">
            <p><strong>📎 Note:</strong> NCLAT PDF documents are attached to this email for your convenience.</p>
        </div>
        <p>This is a test email to verify that PDF attachments work correctly for NCLAT cases.</p>
        <p>If you receive this email with a PDF attachment, the functionality is working!</p>
    </div>
    """
    
    # Create dummy PDF
    dummy_pdf = create_dummy_pdf()
    filename = "test_nclat_case.pdf"
    
    # Prepare attachment
    pdf_attachments = [(dummy_pdf, filename)]
    
    print(f"📧 Email Details:")
    print(f"   Subject: {subject}")
    print(f"   To: <EMAIL>")
    print(f"   Attachments: {len(pdf_attachments)} file")
    print(f"   PDF Size: {len(dummy_pdf)} bytes")
    
    # Ask user confirmation
    send_email = input("\n❓ Send test email with dummy PDF attachment? (y/n): ").lower().strip()
    
    if send_email in ['y', 'yes']:
        try:
            print("\n📤 Sending email...")
            result = funk.sendEmail(
                subject, 
                body, 
                "<EMAIL>", 
                pdf_attachments=pdf_attachments
            )
            print(f"✅ {result}")
            print("\n🎉 Success! Check your email for the PDF attachment.")
            print("   If you received the email with attachment, the basic functionality works!")
            return True
            
        except Exception as e:
            print(f"❌ Error sending email: {e}")
            print("\n🔧 Possible issues:")
            print("   1. Gmail SMTP credentials might be wrong")
            print("   2. Network connectivity issues")
            print("   3. Email attachment size limits")
            return False
    else:
        print("📧 Email not sent (user choice)")
        return False

def test_sendEmail_function():
    """Test the sendEmail function parameters"""
    print("\n🔧 Testing sendEmail Function Parameters")
    print("="*50)
    
    # Test function signature
    try:
        import inspect
        sig = inspect.signature(funk.sendEmail)
        print(f"✅ Function signature: {sig}")
        
        # Check if pdf_attachments parameter exists
        params = list(sig.parameters.keys())
        if 'pdf_attachments' in params:
            print("✅ pdf_attachments parameter found")
        else:
            print("❌ pdf_attachments parameter missing!")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Error checking function: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Email Attachment Test")
    print("="*40)
    print("This tests the basic email attachment functionality")
    print("="*40)
    
    # Test 1: Check function
    if not test_sendEmail_function():
        print("\n❌ Function test failed - check your sendEmail implementation")
        return
    
    # Test 2: Send test email
    test_basic_email_attachment()

if __name__ == "__main__":
    main()
