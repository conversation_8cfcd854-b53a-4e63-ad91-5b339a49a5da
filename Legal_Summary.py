import json
import logging
import os
import sys
from typing import Any, Optional

from google import genai
from google.cloud import storage
from google.genai import types
from google.genai.types import HttpOptions, Part
from google.oauth2 import service_account
from pydantic import BaseModel

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
)

# --- Configuration ---

# Set environment variables for Vertex AI
os.environ["GOOGLE_GENAI_USE_VERTEXAI"] = "True"
# Replace with your actual Google Cloud project ID and location
os.environ["GOOGLE_CLOUD_PROJECT"] = "cciscrape"
os.environ["GOOGLE_CLOUD_LOCATION"] = "global"  # Changed to match official example
os.environ["Google_api_key"] = "AIzaSyANs-Etuztf0YlgI1zTeB0gv2HDW93Sh5A"

# Initialize credentials from service account file
credentials = service_account.Credentials.from_service_account_file(
    "cciscrape-d55fa0df8a7a.json",
    scopes=["https://www.googleapis.com/auth/cloud-platform"],
)

SYSTEM_PROMPT = """You are a legal document analyst specializing in court orders, tribunal decisions, and regulatory determinations.
Instructions:
- Extract critical facts, specific outcomes for involved parties, practical implications, and any novel legal interpretations, definitions, or innovative aspects from legal documents
- Include key details like party names, actions, rulings, consequences, and new legal insights (e.g., reinterpretations of laws or concepts) without attributing to authorities
- Use precise legal terminology and direct phrasing to deliver standalone, fact-based statements rich in document-specific details
- Ensure each point is specific, informative, and useful for lawyers—incorporate essential who, what, why, outcomes, and novel elements for actionable insights
- Be ultra-concise: focus on core determinations, party impacts, implications, and new legal developments without redundancy or embellishments; use shorthand references (e.g., 'above appeal') for previously mentioned items to avoid repetition while preserving clarity (Strictly do this only if its very clear what it the shorthand reference is attributing to and it does not cause ambiguity)
For case heading generation:
- Create an informative, professional heading that captures the essence of the case
- Include actual party names (not generic terms like "Acquirers" or "Appellants"), core legal issue, and case type when identifiable
- Prioritize specific entity names over generic descriptors to ensure clarity and immediate case identification
- Keep headings concise but descriptive (typically 8-15 words)
- Use standard legal case formatting conventions"""

SUMMARY_PROMPT = """From the given legal document PDF, extract and return:
1. A concise, informative case heading that captures the essence of the case including actual party names (avoid generic terms like "Acquirers," "Appellants," or "Respondents"—use specific entity names), core legal issue, and case type (8-15 words maximum)
2. Exactly three key points as a list of concise, standalone statements. Each point must capture a core legal or factual determination, including specific details like party outcomes, events, implications, or novel interpretations (e.g., new definitions or legal concepts)—phrased directly as facts without attributions, explanations, or vagueness; minimize repetition by using shorthand (e.g., 'both appeals') for referenced items—to provide actionable, document-specific insights for lawyers.

Additional case metadata (if provided): 
{case_context}
Return the response in the specified JSON format with both heading and key_points.
"""


class LegalSummary(BaseModel):
    heading: str
    key_points: list[str]


def read_pdf_from_gcs_bucket(gcs_path, credentials):
    """
    Read a PDF file from Google Cloud Storage and return as bytes.

    Args:
        gcs_path (str): Full GCS path like
        credentials: Google Cloud service account credentials json

    Returns:
        bytes: PDF file content as bytes
    """
    try:
        # Parse the GCS path to extract bucket name and blob name
        if gcs_path.startswith("gc://"):
            path_parts = gcs_path[5:]  # Remove 'gc://' prefix
        elif gcs_path.startswith("gs://"):
            path_parts = gcs_path[5:]  # Remove 'gs://' prefix
        else:
            raise ValueError(
                "Invalid GCS path format. Expected 'gc://' or 'gs://' prefix"
            )

        # Split bucket name and file path
        bucket_name, blob_name = path_parts.split("/", 1)

        # Initialize the GCS client with credentials
        client = storage.Client(credentials=credentials)

        # Get the bucket
        bucket = client.bucket(bucket_name)

        # Get the blob (file)
        blob = bucket.blob(blob_name)

        # Check if the file exists
        if not blob.exists():
            raise FileNotFoundError(f"File not found: {gcs_path}")

        # Download the file as bytes
        pdf_bytes = blob.download_as_bytes()

        return pdf_bytes

    except Exception as e:
        print(f"Error reading PDF from GCS: {str(e)}")
        raise


def format_case_context(case_info: Optional[dict]) -> str:
    """
    Format case_info dictionary into a readable context string.

    Args:
        case_info (dict, optional): Dictionary containing case metadata

    Returns:
        str: Formatted context string
    """
    if not case_info:
        return "No additional case context provided."

    context_parts = []
    for key, value in case_info.items():
        if key != "path" and value:
            context_parts.append(f"{key}: {value}")

    return (
        "; ".join(context_parts)
        if context_parts
        else "No additional case context provided."
    )


def generate_legal_summary(
    pdf_path: str,
    case_info: Optional[dict] = None,
    api_key: Optional[str] = "AIzaSyBz5arhy6ZPU7svIVlNRv90FlgRg5kuMdI",
    model: str = "gemini-2.5-flash",
    system_instruction: Optional[str] = SYSTEM_PROMPT,
    temperature: float = 1,
    max_output_tokens: int = 10000,
    response_mime_type: str = "application/json",
    response_schema: Any = LegalSummary,
    credientails_json_path: str = "cciscrape-d55fa0df8a7a.json",
):
    """
    Generate legal summary with case heading from PDF document.

    Args:
        pdf_path (str): GCS path to PDF file
        case_info (dict, optional): Additional case metadata
        api_key (str): Google API key
        model (str): Gemini model to use
        system_instruction (str): System prompt
        temperature (float): Model temperature
        max_output_tokens (int): Maximum output tokens
        response_mime_type (str): Response MIME type
        response_schema: Pydantic model for response schema
        credientails_json_path (str): Path to credentials JSON file

    Returns:
        list: List where first element is heading, rest are key points. Empty list on error.
    """
    try:
        # Load credentials
        try:
            credentials = service_account.Credentials.from_service_account_file(
                credientails_json_path,
                scopes=["https://www.googleapis.com/auth/cloud-platform"],
            )
        except FileNotFoundError as e:
            raise RuntimeError(f"Failed to load service account credentials: {e}")

        # Read PDF from GCS
        try:
            pdf_bytes = read_pdf_from_gcs_bucket(pdf_path, credentials)
            if not pdf_bytes:
                raise ValueError("Empty PDF content retrieved from GCS.")
        except Exception as e:
            raise RuntimeError(f"Error reading PDF from GCS: {e}")

        # Initialize Gemini client
        try:
            client = genai.Client(
                credentials=credentials, http_options=HttpOptions(api_version="v1")
            )

        except Exception as e:
            raise RuntimeError(f"Failed to initialize GenAI client: {e}")

        # Format case context
        case_context = format_case_context(case_info)

        # Format the prompt with case context
        formatted_prompt = SUMMARY_PROMPT.format(case_context=case_context)

        # Generate content
        try:
            response = client.models.generate_content(
                model=model,
                contents=[
                    types.Part.from_bytes(data=pdf_bytes, mime_type="application/pdf"),
                    formatted_prompt,
                ],
                config=types.GenerateContentConfig(
                    system_instruction=system_instruction,
                    temperature=temperature,
                    max_output_tokens=max_output_tokens,
                    response_mime_type=response_mime_type,
                    response_schema=response_schema,
                ),
            )
        except Exception as e:
            raise RuntimeError(f"Model generation failed: {e}")

        # Parse response and return as single list
        try:
            output_json = json.loads(response.text)
            heading = output_json.get("heading", "")
            key_points = output_json.get("key_points", [])

            # Return as single list: [heading, key_point1, key_point2, key_point3]
            return [heading] + key_points

        except (json.JSONDecodeError, AttributeError, TypeError) as e:
            raise RuntimeError(f"Error parsing model response: {e}")

    except Exception as e:
        print(f"[generate_legal_summary] Error: {e}")
        return []


def coreEmail(new_rows, table):
    tdict = {
        "section_31": "Section 31 Case",
        "section_43a_and_44": "Section 43a and 44 Case",
        "cases_approved_with_modifications": "Approved with Modification (s) Case",
        "antitrust_orders": "Antitrust Order",
        "press_releases": "Antitrust Press Release",
        "competition_appeal_orders": "Competition Appeal Order",
    }
    subject = f"📋 New {tdict[table]} Alert - Vidhi AI"

    prep = "are"
    s = "s"
    if len(new_rows) <= 1:
        prep = "is"
        s = ""

    # Professional email header
    body = f"""
    <meta name="color-scheme" content="light">
<meta name="supported-color-schemes" content="light">
    <div style="background: #fff !important; color-scheme: light; padding:0; margin:0;">
      <div style="background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); padding: 20px; border-radius: 8px; margin-bottom: 20px;">
          <h2 style="color: white; margin: 0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
              ⚖️ Legal Case Update from Vidhi AI
          </h2>
          <p style="color: #e8f4f8; margin: 5px 0 0 0; font-size: 0.9em;">
              Automated Legal Intelligence Report
          </p>
      </div>
      <div style="background-color: #f8f9fc; padding: 15px; border-left: 4px solid #2a5298; margin-bottom: 20px; border-radius: 0 6px 6px 0;">
          <p style="margin: 0; font-size: 1.1em; color: #2c3e50;">
              <strong>📢 Update Summary:</strong> There {prep} <span style="color: #2a5298; font-weight: bold;">{len(new_rows)}</span> new {tdict[table]}{s} available for your review.
          </p>
      </div>
    """

    if not new_rows:
        return

    # Get headers from the first row
    if "First_Order_History" in new_rows[0]:
        for i in range(0, len(new_rows)):
            new_rows[i]["First_Order_History"] = json.loads(
                new_rows[i]["First_Order_History"]
            )
            new_rows[i]["Order_Date"] = new_rows[i]["First_Order_History"]["Order_Date"]
            new_rows[i]["Order_Type"] = new_rows[i]["First_Order_History"]["Order_Type"]
            new_rows[i]["Coram"] = new_rows[i]["First_Order_History"]["Coram"]
            new_rows[i].pop("First_Order_History", None)

    headers = list(new_rows[0].keys())

    # Remove summary_list and all columns ending with "*_summary" from headers
    headers_to_remove = []
    for header in headers:
        if header == "summary_list" or header.endswith("_summary"):
            headers_to_remove.append(header)

        if header == "path":
            headers_to_remove.append(header)

    for header in headers_to_remove:
        headers.remove(header)

    summary_cols = [col for col in new_rows[0].keys() if col.endswith("_summary")]

    logging.info("***********************************")
    logging.info(f"Summary columns: {summary_cols}")
    logging.info("***********************************")
    
    #Flag for section 31 override
    override31=False
    if 'order_link_summary' in summary_cols:
        override31 = True

    for row in new_rows:
        longest_summary = ""
        for col in summary_cols:
            val = row.get(col, "")
            if isinstance(val, str) and len(val) > len(longest_summary):
                longest_summary = val

        if longest_summary:

            if override31:
                if len(row['order_link_summary']) > 0: #Something is there
                    longest_summary=row['order_link_summary']

            row["summary_list"] = longest_summary.split("<<<END_OF_ITEM>>>")
        else:
            row["summary_list"] = []
    
    

        logging.info("***********************************")
        logging.info(f"row summary_list: {row.get('summary_list', [])}")
        logging.info("***********************************")
    
    #Override for Section 31: always show order
    



    # Enhanced table styling
    table_style = """
        border-collapse: collapse; 
        width: 100%; 
        box-shadow: 0 2px 8px rgba(0,0,0,0.1); 
        border-radius: 8px; 
        overflow: hidden;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: #fff !important;
"""

    th_style = """
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        color: #ffffff;
        padding: 12px 10px;
        font-size: 0.95em;
        font-weight: 600;
        text-align: left;
        border-bottom: 2px solid #1a252f;
    """

    td_style = """
        border: 1px solid #e1e8ed;
        padding: 10px;
        font-size: 0.9em;
        background-color: #ffffff;
        vertical-align: top;
    """

    # Case details section header
    body += """
    <div style="margin: 25px 0 15px 0;">
        <h3 style="color: #2c3e50; margin: 0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; border-bottom: 2px solid #2a5298; padding-bottom: 8px; display: inline-block;">
            📊 Case Details
        </h3>
    </div>
    """

    # Build HTML table
    body += f'<table style="{table_style}">'

    # Header row with icons
    header_icons = {
        "case_title": "📋",
        "case_number": "🔢",
        "order_date": "📅",
        "order_type": "⚖️",
        "coram": "👨‍⚖️",
        "link": "🔗",
    }

    body += "<tr>"
    for h in headers:
        icon = header_icons.get(h.lower(), "📄")
        header_text = h.replace("_", " ").title()
        body += f'<th style="{th_style}">{icon} {header_text}</th>'
    body += "</tr>"

    # Data rows with alternating colors
    for i, row in enumerate(new_rows):
        row_bg = "#f8f9fc" if i % 2 == 0 else "#ffffff"
        body += f'<tr style="background-color: {row_bg};">'

        for h in headers:
            val = row[h]
            if isinstance(val, str) and (
                val.startswith("http://") or val.startswith("https://")
            ):
                val = f'<a href="{val}" style="color: #2a5298; text-decoration: none; font-weight: 500; border-bottom: 1px solid #2a5298;">🔗 View Document</a>'

            cell_style = td_style.replace("#ffffff", row_bg)
            body += f'<td style="{cell_style}">{val if val is not None else "—"}</td>'
        body += "</tr>"

    body += "</table>"

    # Add summary section if summary data exists
    if len(new_rows) > 0 and "summary_list" in new_rows[0]:
        body += """
        <div style="margin: 30px 0 20px 0;">
            <h3 style="color: #2c3e50; margin: 0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; border-bottom: 2px solid #2a5298; padding-bottom: 8px; display: inline-block;">
                📝 Detailed Case Analysis
            </h3>
        </div>
        """

        for i, row in enumerate(new_rows):
            if "summary_list" in row and row["summary_list"]:
                # case_title = row.get("case_title", f"Case {i + 1}")

                # If summary_list has 4 items, use the first as title
                # TODO:  This is a temporary fix, need to find a better way to handle this
                print("-------------------------------------")
                print(len(row["summary_list"]))
                print(row["summary_list"])
                if len(row["summary_list"]) == 4:
                    case_title = row["summary_list"][0]
                    key_points = row["summary_list"][1:]
                else:
                    case_title = row.get("case_title", f"Case {i + 1}")
                    key_points = row["summary_list"]

                print(case_title)
                print(key_points)
                print("-------------------------------------")

                # Individual summary with enhanced styling
                body += f"""
                <div style="margin-bottom: 25px; background: linear-gradient(135deg, #ffffff 0%, #f8f9fc 100%); 
                           border: 1px solid #e1e8ed; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.05); 
                           overflow: hidden;">
                    
                    <div style="background: linear-gradient(135deg, #2a5298 0%, #3b6cb8 100%); padding: 15px; color: white;">
                        <h4 style="margin: 0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; display: flex; align-items: center;">
                            ⚖️ {case_title}
                        </h4>
                    </div>
                    
                    <div style="padding: 20px;">
                        <h5 style="color: #2c3e50; margin: 0 0 12px 0; font-size: 0.95em; font-weight: 600;">
                            📋 Key Points:
                        </h5>
                        <ul style="margin: 0; padding-left: 20px; list-style-type: disc; list-style-position: outside;">
                """

                for bullet_point in key_points:
                    body += f"""
                        <li style="margin-bottom: 8px; line-height: 1.5; color: #34495e;">
                            {bullet_point}
                        </li>
                    """

                body += """
                        </ul>
                    </div>
                </div>
                """

    # Professional footer
    body += """
    <div style="margin-top: 40px; padding: 20px; background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%); 
               border-radius: 8px; text-align: center;">
        <p style="color: #ecf0f1; margin: 0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
            <strong>⚖️ Vidhi AI</strong><br>
            <span style="font-size: 0.9em; color: #bdc3c7;">
                Automated Legal Intelligence & Case Monitoring System<br>
                📧 For technical support, please contact our team
            </span>
        </p>
    </div>
    
    <div style="margin-top: 15px; text-align: center; font-size: 0.8em; color: #7f8c8d;">
        <p style="margin: 0;">
            🔒 This communication is confidential and intended solely for legal professionals.<br>
            Powered by Vidhi AI
        </p>
    </div>
    """

    body += "</div>"  # Close the outer white

    return subject, body
