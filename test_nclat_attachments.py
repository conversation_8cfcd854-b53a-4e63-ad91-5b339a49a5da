#!/usr/bin/env python3
"""
NCLAT Flow Testing Script
Tests the complete NCLAT PDF attachment functionality end-to-end.
"""

import json
import logging
import time
from google.cloud import bigquery
import cci_func as funk
import Legal_Summary
import nclat_func as nclat

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)

# Test configuration
TEST_EMAIL = "<EMAIL>"  # Your email for testing

def print_section(title):
    """Print a formatted section header"""
    print("\n" + "="*60)
    print(f"🧪 {title}")
    print("="*60)

def print_result(test_name, success, details=""):
    """Print test result"""
    status = "✅ PASSED" if success else "❌ FAILED"
    print(f"{test_name}: {status}")
    if details:
        print(f"   {details}")

def test_1_nclat_data_scraping():
    """Test 1: NCLAT Data Scraping"""
    print_section("TEST 1: NCLAT Data Scraping")

    try:
        print("Fetching NCLAT data...")
        data = funk.mainCall('NCLAT', 'Competition Appeal Orders')

        if data and len(data) > 0:
            print_result("NCLAT Data Fetch", True, f"Fetched {len(data)} cases")

            # Show sample case structure
            sample_key = list(data.keys())[0]
            sample_case = data[sample_key]

            print("\n📋 Sample Case Structure:")
            for key, value in sample_case.items():
                if isinstance(value, str) and len(value) > 100:
                    print(f"   {key}: {str(value)[:100]}...")
                else:
                    print(f"   {key}: {value}")

            # Check for required fields
            required_fields = ['Case_No', 'Case_Title', 'path']
            missing_fields = [field for field in required_fields if field not in sample_case]

            if not missing_fields:
                print_result("Required Fields Check", True, "All required fields present")
            else:
                print_result("Required Fields Check", False, f"Missing: {missing_fields}")

            return data
        else:
            print_result("NCLAT Data Fetch", False, "No data returned")
            return None

    except Exception as e:
        print_result("NCLAT Data Fetch", False, f"Error: {e}")
        return None

def test_2_email_generation(sample_data):
    """Test 2: NCLAT Email Generation"""
    print_section("TEST 2: NCLAT Email Generation")

    if not sample_data:
        # Create mock data for testing
        sample_data = {
            "test_case": {
                "Sr_No": "1",
                "File_Number": "COMP/2024/TEST",
                "Case_No": "CA(AT) No. 999/2024",
                "Case_Title": "Test NCLAT Case for PDF Attachment",
                "Bench": "New Delhi",
                "Status": "Disposed",
                "path": "gs://cciscrape_bkt/NCLAT/test_case_999_2024.pdf",
                "index": "TEST-999-2024",
                "order_summary": "Test summary point 1<<<END_OF_ITEM>>>Test summary point 2<<<END_OF_ITEM>>>Test summary point 3"
            }
        }
        print("⚠️  Using mock data for email generation test")

    try:
        # Convert to list format for email generation
        data_list = list(sample_data.values())[:1]  # Take first case only

        print("Generating NCLAT email...")
        email_result = Legal_Summary.coreEmail(data_list, "competition_appeal_orders")

        # Check return format
        if len(email_result) == 3:
            subject, body, pdf_paths = email_result
            print_result("Email Generation Format", True, "Returns 3 values (subject, body, pdf_paths)")

            print(f"\n📧 Email Details:")
            print(f"   Subject: {subject}")
            print(f"   PDF Paths: {pdf_paths}")

            # Test key features
            tests = {
                "Subject contains 'NCLAT'": "Competition Appeal Order" in subject,
                "Body contains attachment notice": "PDF documents are attached" in body,
                "GCS paths hidden": "gs://" not in body or "See PDF attachment" in body,
                "PDF paths returned": len(pdf_paths) > 0,
                "Attachment notice styling": "📎 Note:" in body
            }

            for test_name, passed in tests.items():
                print_result(test_name, passed)

            return subject, body, pdf_paths

        else:
            print_result("Email Generation Format", False, f"Expected 3 values, got {len(email_result)}")
            return None, None, []

    except Exception as e:
        print_result("Email Generation", False, f"Error: {e}")
        return None, None, []

def test_3_gcs_pdf_download(pdf_paths):
    """Test 3: GCS PDF Download"""
    print_section("TEST 3: GCS PDF Download")

    if not pdf_paths:
        print("⚠️  No PDF paths to test - using sample path")
        pdf_paths = ["gs://cciscrape_bkt/NCLAT/sample_test.pdf"]

    successful_downloads = []

    for i, pdf_path in enumerate(pdf_paths[:2]):  # Test max 2 files
        try:
            print(f"Testing download {i+1}: {pdf_path}")
            pdf_bytes, filename = funk.download_pdf_from_gcs(pdf_path)

            if pdf_bytes and filename:
                print_result(f"Download {i+1}", True, f"{filename} ({len(pdf_bytes)} bytes)")
                successful_downloads.append((pdf_bytes, filename))
            else:
                print_result(f"Download {i+1}", False, "No data returned")

        except Exception as e:
            print_result(f"Download {i+1}", False, f"Error: {e}")

    return successful_downloads

def test_4_email_sending(subject, body, pdf_attachments):
    """Test 4: Email Sending with Attachments"""
    print_section("TEST 4: Email Sending with Attachments")

    if not subject or not body:
        print_result("Email Sending", False, "No email content to send")
        return False

    try:
        print(f"Preparing to send email to: {TEST_EMAIL}")
        print(f"Attachments: {len(pdf_attachments)} files")

        # Test email sending function (without actually sending)
        print("Testing email function parameters...")

        # Validate parameters
        tests = {
            "Subject not empty": bool(subject.strip()),
            "Body not empty": bool(body.strip()),
            "Valid email address": "@" in TEST_EMAIL and "." in TEST_EMAIL,
            "Attachments prepared": len(pdf_attachments) > 0
        }

        all_passed = True
        for test_name, passed in tests.items():
            print_result(test_name, passed)
            if not passed:
                all_passed = False

        if all_passed:
            print("\n🚀 Ready to send email!")
            print("   Uncomment the line below to actually send the test email:")
            print(f"   # funk.sendEmail('{subject[:30]}...', body, '{TEST_EMAIL}', pdf_attachments=pdf_attachments)")

            # Uncomment the next line to actually send the email
            # funk.sendEmail(subject, body, TEST_EMAIL, pdf_attachments=pdf_attachments)

            print_result("Email Function Test", True, "All parameters valid")
            return True
        else:
            print_result("Email Function Test", False, "Invalid parameters")
            return False

    except Exception as e:
        print_result("Email Sending", False, f"Error: {e}")
        return False

def test_5_database_integration():
    """Test 5: Database Integration"""
    print_section("TEST 5: Database Integration")

    try:
        print("Reading NCLAT data from database...")
        db_data = funk.read("Competition Appeal Orders", top=3)

        if db_data and len(db_data) > 0:
            print_result("Database Read", True, f"Found {len(db_data)} records")

            # Check data structure
            sample_record = db_data[0]
            required_fields = ['Case_No', 'Case_Title']

            missing_fields = [field for field in required_fields if field not in sample_record]

            if not missing_fields:
                print_result("Database Schema", True, "Required fields present")
            else:
                print_result("Database Schema", False, f"Missing: {missing_fields}")

            # Test email generation with DB data
            print("\nTesting email generation with database data...")
            email_result = Legal_Summary.coreEmail(db_data[:1], "competition_appeal_orders")

            if len(email_result) == 3:
                print_result("DB Email Generation", True, "Successfully generated email from DB data")
                return True
            else:
                print_result("DB Email Generation", False, "Wrong return format")
                return False

        else:
            print_result("Database Read", False, "No data found")
            return False

    except Exception as e:
        print_result("Database Integration", False, f"Error: {e}")
        return False

def test_6_comparison_with_cci():
    """Test 6: Comparison with CCI Email Generation"""
    print_section("TEST 6: CCI vs NCLAT Email Comparison")

    # Sample CCI data
    cci_data = [{
        "case_no": "Case No. 123/2024",
        "description": "Sample CCI Antitrust Case",
        "order_date": "01/01/2024",
        "orders": "https://www.cci.gov.in/sample-order.pdf"
    }]

    # Sample NCLAT data
    nclat_data = [{
        "Case_No": "CA(AT) No. 456/2024",
        "Case_Title": "Sample NCLAT Case",
        "path": "gs://cciscrape_bkt/NCLAT/sample.pdf"
    }]

    try:
        # Test CCI email
        cci_result = Legal_Summary.coreEmail(cci_data, "antitrust_orders")
        nclat_result = Legal_Summary.coreEmail(nclat_data, "competition_appeal_orders")

        # CCI should return 2 values
        cci_passed = len(cci_result) == 2
        print_result("CCI Email Format", cci_passed, f"Returns {len(cci_result)} values")

        # NCLAT should return 3 values
        nclat_passed = len(nclat_result) == 3
        print_result("NCLAT Email Format", nclat_passed, f"Returns {len(nclat_result)} values")

        if cci_passed and nclat_passed:
            cci_subject, cci_body = cci_result
            nclat_subject, nclat_body, nclat_paths = nclat_result

            # Check differences
            tests = {
                "CCI has clickable links": "href=" in cci_body,
                "NCLAT has attachment notice": "PDF documents are attached" in nclat_body,
                "CCI no attachment notice": "PDF documents are attached" not in cci_body,
                "NCLAT hides GCS paths": "gs://" not in nclat_body
            }

            for test_name, passed in tests.items():
                print_result(test_name, passed)

        return cci_passed and nclat_passed

    except Exception as e:
        print_result("CCI vs NCLAT Comparison", False, f"Error: {e}")
        return False

def run_nclat_flow_test():
    """Run the complete NCLAT flow test"""
    print("🚀 NCLAT PDF Attachment Flow Test")
    print("="*60)
    print(f"Test Email: {TEST_EMAIL}")
    print("="*60)

    # Track test results
    results = {}

    # Test 1: Data Scraping
    nclat_data = test_1_nclat_data_scraping()
    results["data_scraping"] = nclat_data is not None

    # Test 2: Email Generation
    subject, body, pdf_paths = test_2_email_generation(nclat_data)
    results["email_generation"] = subject is not None

    # Test 3: GCS PDF Download
    pdf_attachments = test_3_gcs_pdf_download(pdf_paths)
    results["gcs_download"] = len(pdf_attachments) > 0

    # Test 4: Email Sending
    results["email_sending"] = test_4_email_sending(subject, body, pdf_attachments)

    # Test 5: Database Integration
    results["database_integration"] = test_5_database_integration()

    # Test 6: CCI vs NCLAT Comparison
    results["cci_comparison"] = test_6_comparison_with_cci()

    # Summary
    print_section("TEST SUMMARY")

    total_tests = len(results)
    passed_tests = sum(results.values())

    print("📊 Individual Test Results:")
    for test_name, passed in results.items():
        status = "✅ PASSED" if passed else "❌ FAILED"
        formatted_name = test_name.replace('_', ' ').title()
        print(f"   {formatted_name}: {status}")

    print(f"\n🎯 Overall Result: {passed_tests}/{total_tests} tests passed")

    if passed_tests == total_tests:
        print("\n🎉 SUCCESS! All NCLAT flow tests passed!")
        print("   Your NCLAT PDF attachment functionality is working correctly.")
        print(f"   To send a real test email, uncomment line 197 and run again.")
    elif passed_tests >= total_tests * 0.8:
        print("\n⚠️  MOSTLY WORKING! Most tests passed with some minor issues.")
        print("   Check the failed tests above for details.")
    else:
        print("\n❌ ISSUES DETECTED! Several tests failed.")
        print("   Please review the errors above and fix the issues.")

    return results

def run_quick_test():
    """Run a quick test with mock data only"""
    print("⚡ Quick NCLAT Test (Mock Data Only)")
    print("="*40)

    # Test email generation with mock data
    subject, body, pdf_paths = test_2_email_generation(None)

    # Test CCI vs NCLAT comparison
    comparison_result = test_6_comparison_with_cci()

    if subject and comparison_result:
        print("\n✅ Quick test PASSED! Basic functionality working.")
        print("   Run full test with: python test_nclat_attachments.py --full")
    else:
        print("\n❌ Quick test FAILED! Check the errors above.")

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "--quick":
        run_quick_test()
    else:
        print("🧪 NCLAT Flow Testing Script")
        print("="*40)
        print("Options:")
        print("  python test_nclat_attachments.py        # Full test")
        print("  python test_nclat_attachments.py --quick # Quick test")
        print()

        choice = input("Run full test? (y/n): ").lower().strip()
        if choice in ['y', 'yes']:
            run_nclat_flow_test()
        else:
            run_quick_test()