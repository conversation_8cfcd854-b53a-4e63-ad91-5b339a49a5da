#!/usr/bin/env python3
"""
Test script to check real NCLAT PDFs and send actual email with attachments
"""

import cci_func as funk
import Legal_Summary
from google.cloud import storage

def check_real_nclat_data():
    """Check what NCLAT data exists in database"""
    print("🔍 Checking Real NCLAT Data in Database")
    print("="*50)
    
    try:
        # Get NCLAT data from database
        db_data = funk.read("Competition Appeal Orders", top=5)
        
        if not db_data:
            print("❌ No NCLAT data found in database")
            return None
        
        print(f"✅ Found {len(db_data)} NCLAT records in database")
        
        # Check which records have PDF paths
        records_with_pdfs = []
        for i, record in enumerate(db_data):
            print(f"\n📋 Record {i+1}:")
            print(f"   Case No: {record.get('Case_No', 'N/A')}")
            print(f"   Case Title: {record.get('Case_Title', 'N/A')[:60]}...")
            
            # Check for path field
            if 'path' in record and record['path']:
                pdf_path = record['path']
                print(f"   PDF Path: {pdf_path}")
                records_with_pdfs.append(record)
            else:
                print("   PDF Path: ❌ Not found")
        
        print(f"\n📊 Summary: {len(records_with_pdfs)}/{len(db_data)} records have PDF paths")
        return records_with_pdfs
        
    except Exception as e:
        print(f"❌ Error checking database: {e}")
        return None

def check_gcs_bucket():
    """Check what PDFs actually exist in GCS bucket"""
    print("\n🗂️ Checking GCS Bucket Contents")
    print("="*50)
    
    try:
        # Initialize GCS client
        storage_client = storage.Client.from_service_account_json("cciscrape-d55fa0df8a7a.json")
        bucket = storage_client.bucket("cciscrape_bkt")
        
        # List NCLAT folder contents
        blobs = list(bucket.list_blobs(prefix="NCLAT/"))
        
        if not blobs:
            print("❌ No files found in NCLAT folder")
            return []
        
        print(f"✅ Found {len(blobs)} files in NCLAT folder:")
        
        pdf_files = []
        for blob in blobs[:10]:  # Show first 10 files
            if blob.name.endswith('.pdf'):
                size_mb = blob.size / (1024 * 1024) if blob.size else 0
                print(f"   📄 {blob.name} ({size_mb:.2f} MB)")
                pdf_files.append(f"gs://cciscrape_bkt/{blob.name}")
            else:
                print(f"   📁 {blob.name}")
        
        print(f"\n📊 Summary: {len(pdf_files)} PDF files found")
        return pdf_files
        
    except Exception as e:
        print(f"❌ Error checking GCS bucket: {e}")
        return []

def test_pdf_download(pdf_path):
    """Test downloading a specific PDF"""
    print(f"\n⬇️ Testing PDF Download: {pdf_path}")
    print("="*50)
    
    try:
        pdf_bytes, filename = funk.download_pdf_from_gcs(pdf_path)
        
        if pdf_bytes and filename:
            size_mb = len(pdf_bytes) / (1024 * 1024)
            print(f"✅ Successfully downloaded: {filename}")
            print(f"   Size: {size_mb:.2f} MB ({len(pdf_bytes)} bytes)")
            return pdf_bytes, filename
        else:
            print(f"❌ Failed to download PDF")
            return None, None
            
    except Exception as e:
        print(f"❌ Error downloading PDF: {e}")
        return None, None

def test_email_with_real_attachment():
    """Test sending email with real PDF attachment"""
    print("\n📧 Testing Email with Real PDF Attachment")
    print("="*50)
    
    # Get real NCLAT data
    records_with_pdfs = check_real_nclat_data()
    
    if not records_with_pdfs:
        print("❌ No NCLAT records with PDF paths found")
        return False
    
    # Take the first record with a PDF
    test_record = records_with_pdfs[0]
    pdf_path = test_record['path']
    
    print(f"\n🎯 Testing with record:")
    print(f"   Case: {test_record.get('Case_No', 'N/A')}")
    print(f"   PDF: {pdf_path}")
    
    # Test PDF download
    pdf_bytes, filename = test_pdf_download(pdf_path)
    
    if not pdf_bytes:
        print("❌ Cannot test email - PDF download failed")
        return False
    
    # Generate email
    print(f"\n📝 Generating email...")
    try:
        email_result = Legal_Summary.coreEmail([test_record], "competition_appeal_orders")
        
        if len(email_result) == 3:
            subject, body, pdf_paths = email_result
            print(f"✅ Email generated successfully")
            print(f"   Subject: {subject}")
            print(f"   PDF paths in email: {pdf_paths}")
            
            # Prepare attachment
            pdf_attachments = [(pdf_bytes, filename)]
            
            print(f"\n🚀 Ready to send email!")
            print(f"   To: <EMAIL>")
            print(f"   Attachments: {len(pdf_attachments)} file(s)")
            print(f"   Total size: {len(pdf_bytes) / (1024 * 1024):.2f} MB")
            
            # Ask user if they want to send
            send_email = input("\n❓ Send test email? (y/n): ").lower().strip()
            
            if send_email in ['y', 'yes']:
                try:
                    result = funk.sendEmail(
                        subject, 
                        body, 
                        "<EMAIL>", 
                        pdf_attachments=pdf_attachments
                    )
                    print(f"✅ {result}")
                    return True
                except Exception as e:
                    print(f"❌ Error sending email: {e}")
                    return False
            else:
                print("📧 Email not sent (user choice)")
                return True
                
        else:
            print(f"❌ Email generation failed - wrong format")
            return False
            
    except Exception as e:
        print(f"❌ Error generating email: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 Real NCLAT PDF Attachment Test")
    print("="*60)
    print("This script tests with REAL data from your database and GCS bucket")
    print("="*60)
    
    # Step 1: Check database
    records_with_pdfs = check_real_nclat_data()
    
    # Step 2: Check GCS bucket
    gcs_pdfs = check_gcs_bucket()
    
    # Step 3: Test with real data
    if records_with_pdfs and gcs_pdfs:
        print("\n✅ Both database records and GCS PDFs found!")
        test_email_with_real_attachment()
    elif records_with_pdfs:
        print("\n⚠️ Database records found but checking GCS PDFs...")
        test_email_with_real_attachment()
    else:
        print("\n❌ No NCLAT data with PDFs found")
        print("   You may need to run the NCLAT scraper first to get data with PDFs")

if __name__ == "__main__":
    main()
