# app.py

import os
import re
import threading
import time

import requests
from flask import Flask, jsonify, render_template, request

import cci_func as funk
import nclat_func as nclat

app = Flask(__name__)

# Globals and Init Job
global Schemas, DATA
Schemas = {
    "Combination": {
        "Section 31": [
            "col_no",
            "combination_registration_no",
            "notifying_parties",
            "form",
            "date_of_notification",
            "status",
            "decision_date",
            "summary_as_submitted_by_parties",
            "order_link",
            "media",
        ],
        "Section 43a and 44": [
            "col_no",
            "combination_registration_no",
            "description",
            "under_section",
            "decision_date",
            "order_link",
            "media",
        ],
        "Cases Approved with Modification (s)": [
            "col_no",
            "case_no",
            "parties_name",
            "date_of_order",
        ],
    },
    "Antitrust": {
        "Orders": [
            "col_no",
            "case_no",
            "description",
            "type",
            "date_of_main_order",
            "date_of_order",
            "orders",
        ],
        "Press Releases": ["col_no", "title", "date_of_release", "document"],
    },
    "NCLAT": {
        "Competition Appeal Orders": [
            "Sr_No",
            "File_Number",
            "Case_No",
            "Case_Title",
            "Bench",
            "Status",
        ]
    },
}

DATA = {}
for Cat in Schemas:
    for key in Schemas[Cat]:
        try:

            DATA[key] = funk.mainCall(Cat, key)

        except Exception as e:
            print(f"Error fetching data for {Cat} - {key}: {e}")
            time.sleep(60)
            try:
                DATA[key] = funk.mainCall(Cat, key)

            except Exception as e:
                print(f"Retry failed for {Cat} - {key}: {e}")
                continue
            continue
        continue


for key in DATA:
    print(key, len(DATA[key]))


# Ok, now to check if there is new data.
@app.route("/")
def index():

    return render_template("index.html")


@app.route("/job")
def runjob():

    DATA = {}
    for Cat in Schemas:
        for key in Schemas[Cat]:
            try:

                DATA[key] = funk.mainCall(Cat, key)

            except Exception as e:
                print(f"Error fetching data for {Cat} - {key}: {e}")
                time.sleep(60)
                try:
                    DATA[key] = funk.mainCall(Cat, key)

                except Exception as e:
                    print(f"Retry failed for {Cat} - {key}: {e}")
                    continue
                continue
            continue

    for key in DATA:
        print(key, len(DATA[key]))

    return "Job completed"


@app.route("/get_data", methods=["POST"])
def get_data():

    req = request.json
    section = req.get("section")
    # Ok, now based on the section, return the data.

    print(section)

    data = funk.read(section)

    return jsonify(data)


# --- EMAIL HANDLING ---
EMAIL_FILE = os.path.join("data", "emails.txt")


def get_all_emails():
    if not os.path.exists(EMAIL_FILE):
        return []
    with open(EMAIL_FILE, "r") as f:
        emails = [line.strip() for line in f if line.strip()]
    return emails


def save_all_emails(emails):
    with open(EMAIL_FILE, "w") as f:
        for email in emails:
            f.write(email + "\n")


def is_valid_email(email):
    # Simple regex for email validation
    return re.match(r"^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$", email)


@app.route("/emails")
def list_emails():
    return jsonify(get_all_emails())


@app.route("/add_email", methods=["POST"])
def add_email():
    data = request.json
    email = data.get("email", "").strip()
    if not is_valid_email(email):
        return jsonify({"success": False, "error": "Invalid email address."})
    emails = get_all_emails()
    if email in emails:
        return jsonify({"success": False, "error": "Email already exists."})
    emails.append(email)
    save_all_emails(emails)
    funk.newSubscriber(email)
    return jsonify({"success": True})


@app.route("/delete_email", methods=["POST"])
def delete_email():
    data = request.json
    email = data.get("email", "").strip()
    emails = get_all_emails()
    if email in emails:
        emails.remove(email)
        save_all_emails(emails)
        return jsonify({"success": True})
    return jsonify({"success": False, "error": "Email not found."})


def periodic_job():
    with app.app_context():
        print("Running scheduled job...")
        runjob()
    # Schedule the next run in 1800 seconds (30 minutes)
    threading.Timer(1800, periodic_job).start()


# Start the periodic job when the app starts
periodic_job()


if __name__ == "__main__":
    # Ensure data directory exists
    import os

    app.run(host="0.0.0.0", port=int(os.environ.get("PORT", 8080)))
