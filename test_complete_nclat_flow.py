#!/usr/bin/env python3
"""
Complete NCLAT Flow Test Script
Tests the entire NCLAT pipeline from scraping to email delivery
"""

import json
import logging
import time
from datetime import datetime
from google.cloud import bigquery, storage
import cci_func as funk
import Legal_Summary
import nclat_func as nclat

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)

# Test configuration
TEST_EMAIL = "<EMAIL>"
BACKUP_EMAIL = "<EMAIL>"

def print_header(title, char="=", width=70):
    """Print formatted header"""
    print(f"\n{char * width}")
    print(f"🧪 {title}")
    print(f"{char * width}")

def print_step(step_num, title):
    """Print step header"""
    print(f"\n{'='*20} STEP {step_num}: {title} {'='*20}")

def print_result(test_name, success, details=""):
    """Print test result"""
    status = "✅ PASSED" if success else "❌ FAILED"
    print(f"{test_name}: {status}")
    if details:
        print(f"   {details}")

def step_1_test_nclat_scraping():
    """Step 1: Test NCLAT Data Scraping"""
    print_step(1, "NCLAT Data Scraping")
    
    try:
        print("🔍 Attempting to scrape fresh NCLAT data...")
        start_time = time.time()
        
        # Attempt to scrape new NCLAT data
        scraped_data = funk.mainCall('NCLAT', 'Competition Appeal Orders')
        
        scrape_time = time.time() - start_time
        
        if scraped_data and len(scraped_data) > 0:
            print_result("NCLAT Scraping", True, f"Scraped {len(scraped_data)} cases in {scrape_time:.1f}s")
            
            # Show sample scraped data
            sample_key = list(scraped_data.keys())[0]
            sample_case = scraped_data[sample_key]
            
            print(f"\n📋 Sample Scraped Case:")
            print(f"   Case No: {sample_case.get('Case_No', 'N/A')}")
            print(f"   Title: {sample_case.get('Case_Title', 'N/A')[:60]}...")
            print(f"   PDF Path: {sample_case.get('path', 'N/A')}")
            
            return scraped_data, True
        else:
            print_result("NCLAT Scraping", False, "No data scraped (may be network/site issues)")
            return None, False
            
    except Exception as e:
        print_result("NCLAT Scraping", False, f"Error: {e}")
        return None, False

def step_2_test_database_operations(scraped_data):
    """Step 2: Test Database Write and Read Operations"""
    print_step(2, "Database Operations")
    
    # Test database read (existing data)
    try:
        print("📖 Reading existing NCLAT data from database...")
        existing_data = funk.read("Competition Appeal Orders", top=5)
        
        if existing_data:
            print_result("Database Read", True, f"Found {len(existing_data)} existing records")
        else:
            print_result("Database Read", False, "No existing data found")
            
    except Exception as e:
        print_result("Database Read", False, f"Error: {e}")
        existing_data = None
    
    # Test database write (if we have new scraped data)
    if scraped_data:
        try:
            print("💾 Testing database write with scraped data...")
            
            # Convert scraped data to list format for database write
            data_list = list(scraped_data.values())
            
            # Test the write function (this will also trigger email sending)
            print("⚠️  Note: This will trigger the complete pipeline including emails!")
            proceed = input("Continue with database write? (y/n): ").lower().strip()
            
            if proceed in ['y', 'yes']:
                funk.write(data_list, 'competition_appeal_orders', 'index')
                print_result("Database Write", True, "Data written and emails triggered")
                return existing_data, True
            else:
                print_result("Database Write", False, "Skipped by user choice")
                return existing_data, False
                
        except Exception as e:
            print_result("Database Write", False, f"Error: {e}")
            return existing_data, False
    else:
        print("⚠️  No scraped data to write to database")
        return existing_data, existing_data is not None

def step_3_test_email_generation():
    """Step 3: Test Email Generation"""
    print_step(3, "Email Generation")
    
    try:
        # Get sample data for email generation
        print("📧 Testing email generation with database data...")
        db_data = funk.read("Competition Appeal Orders", top=2)
        
        if not db_data:
            print_result("Email Generation", False, "No data available for email generation")
            return None, None, []
        
        # Test NCLAT email generation
        email_result = Legal_Summary.coreEmail(db_data, "competition_appeal_orders")
        
        if len(email_result) == 3:
            subject, body, pdf_paths = email_result
            print_result("NCLAT Email Generation", True, f"Generated email with {len(pdf_paths)} PDF paths")
            
            print(f"\n📧 Email Details:")
            print(f"   Subject: {subject}")
            print(f"   PDF Paths: {len(pdf_paths)} files")
            for i, path in enumerate(pdf_paths[:3]):  # Show first 3
                print(f"     {i+1}. {path.split('/')[-1]}")
            
            # Test key features
            features = {
                "Has attachment notice": "PDF documents are attached" in body,
                "Hides GCS paths": "gs://" not in body or "See PDF attachment" in body,
                "Professional styling": "📎 Note:" in body,
                "Returns PDF paths": len(pdf_paths) > 0
            }
            
            for feature, passed in features.items():
                print_result(f"  {feature}", passed)
            
            return subject, body, pdf_paths
        else:
            print_result("NCLAT Email Generation", False, f"Wrong return format: {len(email_result)} values")
            return None, None, []
            
    except Exception as e:
        print_result("Email Generation", False, f"Error: {e}")
        return None, None, []

def step_4_test_pdf_operations(pdf_paths):
    """Step 4: Test PDF Download and Attachment Preparation"""
    print_step(4, "PDF Operations")
    
    if not pdf_paths:
        print_result("PDF Operations", False, "No PDF paths to test")
        return []
    
    successful_downloads = []
    total_size = 0
    
    print(f"⬇️ Testing PDF downloads for {len(pdf_paths)} files...")
    
    for i, pdf_path in enumerate(pdf_paths[:3]):  # Test max 3 files
        try:
            print(f"   Downloading {i+1}/{min(len(pdf_paths), 3)}: {pdf_path.split('/')[-1]}")
            
            pdf_bytes, filename = funk.download_pdf_from_gcs(pdf_path)
            
            if pdf_bytes and filename:
                size_mb = len(pdf_bytes) / (1024 * 1024)
                total_size += len(pdf_bytes)
                successful_downloads.append((pdf_bytes, filename))
                print_result(f"  Download {i+1}", True, f"{filename} ({size_mb:.2f} MB)")
            else:
                print_result(f"  Download {i+1}", False, "No data returned")
                
        except Exception as e:
            print_result(f"  Download {i+1}", False, f"Error: {e}")
    
    if successful_downloads:
        total_mb = total_size / (1024 * 1024)
        print_result("PDF Downloads", True, f"{len(successful_downloads)} files, {total_mb:.2f} MB total")
    else:
        print_result("PDF Downloads", False, "No PDFs downloaded successfully")
    
    return successful_downloads

def step_5_test_email_sending(subject, body, pdf_attachments):
    """Step 5: Test Email Sending with Attachments"""
    print_step(5, "Email Sending")
    
    if not subject or not body:
        print_result("Email Sending", False, "No email content available")
        return False
    
    if not pdf_attachments:
        print_result("Email Sending", False, "No PDF attachments available")
        return False
    
    try:
        print(f"📤 Preparing to send email...")
        print(f"   To: {TEST_EMAIL}")
        print(f"   Subject: {subject}")
        print(f"   Attachments: {len(pdf_attachments)} files")
        
        total_size = sum(len(pdf_bytes) for pdf_bytes, _ in pdf_attachments)
        print(f"   Total size: {total_size / (1024 * 1024):.2f} MB")
        
        # Show attachment details
        print(f"\n📎 Attachments:")
        for i, (pdf_bytes, filename) in enumerate(pdf_attachments):
            size_mb = len(pdf_bytes) / (1024 * 1024)
            print(f"   {i+1}. {filename} ({size_mb:.2f} MB)")
        
        # Ask for confirmation
        send_email = input(f"\n❓ Send test email to {TEST_EMAIL}? (y/n): ").lower().strip()
        
        if send_email in ['y', 'yes']:
            print("📧 Sending email...")
            result = funk.sendEmail(subject, body, TEST_EMAIL, pdf_attachments=pdf_attachments)
            print_result("Email Sending", True, result)
            
            # Optionally send to backup email
            send_backup = input(f"❓ Also send to backup email {BACKUP_EMAIL}? (y/n): ").lower().strip()
            if send_backup in ['y', 'yes']:
                result2 = funk.sendEmail(subject, body, BACKUP_EMAIL, pdf_attachments=pdf_attachments)
                print_result("Backup Email", True, result2)
            
            return True
        else:
            print_result("Email Sending", False, "Skipped by user choice")
            return False
            
    except Exception as e:
        print_result("Email Sending", False, f"Error: {e}")
        return False

def step_6_test_comparison_with_cci():
    """Step 6: Test CCI vs NCLAT Email Comparison"""
    print_step(6, "CCI vs NCLAT Comparison")
    
    try:
        # Test CCI email (should have links)
        print("🔗 Testing CCI email generation...")
        cci_data = funk.read("Orders", top=1)
        
        if cci_data:
            cci_result = Legal_Summary.coreEmail(cci_data, "antitrust_orders")
            cci_passed = len(cci_result) == 2
            print_result("CCI Email Format", cci_passed, f"Returns {len(cci_result)} values")
            
            if cci_passed:
                cci_subject, cci_body = cci_result
                has_links = "href=" in cci_body
                no_attachment_notice = "PDF documents are attached" not in cci_body
                print_result("CCI Has Links", has_links)
                print_result("CCI No Attachment Notice", no_attachment_notice)
        else:
            print_result("CCI Email Test", False, "No CCI data available")
        
        # Test NCLAT email (should have attachment notice)
        print("📎 Testing NCLAT email generation...")
        nclat_data = funk.read("Competition Appeal Orders", top=1)
        
        if nclat_data:
            nclat_result = Legal_Summary.coreEmail(nclat_data, "competition_appeal_orders")
            nclat_passed = len(nclat_result) == 3
            print_result("NCLAT Email Format", nclat_passed, f"Returns {len(nclat_result)} values")
            
            if nclat_passed:
                nclat_subject, nclat_body, nclat_paths = nclat_result
                has_attachment_notice = "PDF documents are attached" in nclat_body
                hides_gcs_paths = "gs://" not in nclat_body
                print_result("NCLAT Has Attachment Notice", has_attachment_notice)
                print_result("NCLAT Hides GCS Paths", hides_gcs_paths)
        else:
            print_result("NCLAT Email Test", False, "No NCLAT data available")
        
        return True
        
    except Exception as e:
        print_result("CCI vs NCLAT Comparison", False, f"Error: {e}")
        return False

def run_complete_nclat_flow_test():
    """Run the complete NCLAT flow test"""
    print_header("COMPLETE NCLAT FLOW TEST", "=", 80)
    print(f"🕒 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📧 Test Email: {TEST_EMAIL}")
    print(f"📧 Backup Email: {BACKUP_EMAIL}")
    
    # Track results
    results = {}
    
    # Step 1: NCLAT Scraping
    scraped_data, scraping_success = step_1_test_nclat_scraping()
    results["scraping"] = scraping_success
    
    # Step 2: Database Operations
    existing_data, db_success = step_2_test_database_operations(scraped_data)
    results["database"] = db_success
    
    # Step 3: Email Generation
    subject, body, pdf_paths = step_3_test_email_generation()
    results["email_generation"] = subject is not None
    
    # Step 4: PDF Operations
    pdf_attachments = step_4_test_pdf_operations(pdf_paths)
    results["pdf_operations"] = len(pdf_attachments) > 0
    
    # Step 5: Email Sending
    email_success = step_5_test_email_sending(subject, body, pdf_attachments)
    results["email_sending"] = email_success
    
    # Step 6: CCI vs NCLAT Comparison
    comparison_success = step_6_test_comparison_with_cci()
    results["comparison"] = comparison_success
    
    # Final Summary
    print_header("FINAL SUMMARY", "=", 80)
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    
    print("📊 Test Results:")
    for test_name, passed in results.items():
        status = "✅ PASSED" if passed else "❌ FAILED"
        formatted_name = test_name.replace('_', ' ').title()
        print(f"   {formatted_name}: {status}")
    
    print(f"\n🎯 Overall Score: {passed_tests}/{total_tests} tests passed ({passed_tests/total_tests*100:.1f}%)")
    
    if passed_tests == total_tests:
        print("\n🎉 PERFECT! Complete NCLAT flow is working flawlessly!")
    elif passed_tests >= total_tests * 0.8:
        print("\n✅ EXCELLENT! NCLAT flow is mostly working with minor issues.")
    elif passed_tests >= total_tests * 0.6:
        print("\n⚠️  GOOD! Core functionality works, some components need attention.")
    else:
        print("\n❌ ISSUES! Several components need fixing.")
    
    print(f"\n🕒 Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return results

if __name__ == "__main__":
    print("🚀 Complete NCLAT Flow Test")
    print("="*50)
    print("This script tests the entire NCLAT pipeline:")
    print("1. Data scraping from NCLAT website")
    print("2. Database write/read operations") 
    print("3. Email generation with attachments")
    print("4. PDF download from GCS")
    print("5. Email sending with PDF attachments")
    print("6. CCI vs NCLAT comparison")
    print("="*50)
    
    proceed = input("🤔 Run complete NCLAT flow test? (y/n): ").lower().strip()
    if proceed in ['y', 'yes']:
        run_complete_nclat_flow_test()
    else:
        print("👋 Test cancelled by user")
