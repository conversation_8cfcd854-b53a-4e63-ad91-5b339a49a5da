
import json
import requests
import cci_func as funk
from google.cloud import storage

import Legal_Summary
from google.cloud import bigquery
import bigquery_io as bq

import nclat_func as nclat
import ssl
import urllib3
from urllib.parse import urlparse

import gemini_find as gf

from dotenv import load_dotenv

def sendEmail(filename='data/emails.txt'):
    with open(filename, 'r') as file:
        emails = file.readlines()
    
    emails = [email.strip() for email in emails if email.strip()]

    bq_client = bigquery.Client.from_service_account_json("cciscrape-d55fa0df8a7a.json")
    project = bq_client.project
    dataset = 'scrapedb'
    # Use BigQueryWriter from bigquery_io.py    

    table='section_31'
    query = f"""SELECT * FROM `cciscrape.scrapedb.section_31` 
    WHERE index='C-2025/05/1284-01/07/2025' AND order_link IS NOT NULL
    ORDER BY PARSE_DATE('%d/%m/%Y', decision_date) DESC"""

    query_job = bq_client.query(query)
    
    results = [dict(row) for row in query_job]

    subject,body=Legal_Summary.coreEmail(results, table)

    # Assuming you have a function to send emails
    for email in emails:

        print(f"Sending email to {email}")
        funk.sendEmail(subject,body,email)
        # send_email_function(email)  # Uncomment and implement this function


if __name__ == "__main__":

    sendEmail()

